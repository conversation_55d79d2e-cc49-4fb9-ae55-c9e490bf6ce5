# Disable config server for tests
spring.cloud.config.enabled=false
spring.cloud.config.import-check.enabled=false

# Configuration de la base de données H2 en mémoire pour les tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=PostgreSQL
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# Configuration JPA/Hibernate
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Configuration H2 Console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Désactiver Eureka pour les tests
eureka.client.enabled=false

# Configuration Kafka pour les tests (désactivé)
spring.kafka.bootstrap-servers=
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

spring.application.name=invitation-service
spring.profiles.active=test
spring.config.import=configserver:http://localhost:8888 