{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/data-source-D34wiQZj.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/recycle-view-repeater-strategy-DoWdPqVw.mjs"], "sourcesContent": ["import { ConnectableObservable } from 'rxjs';\nclass DataSource {}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n  // Check if the value is a DataSource by observing if it has a connect function. Cannot\n  // be checked as an `instanceof DataSource` since people could create their own sources\n  // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n  // here, because of some internal apps.\n  return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\nexport { DataSource as D, isDataSource as i };\n", "import { isObservable, of } from 'rxjs';\nimport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport { InjectionToken } from '@angular/core';\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n  _data;\n  constructor(_data) {\n    super();\n    this._data = _data;\n  }\n  connect() {\n    return isObservable(this._data) ? this._data : of(this._data);\n  }\n  disconnect() {}\n}\n\n/** Indicates how a view was changed by a {@link _ViewRepeater}. */\nvar _ViewRepeaterOperation;\n(function (_ViewRepeaterOperation) {\n  /** The content of an existing view was replaced with another item. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"REPLACED\"] = 0] = \"REPLACED\";\n  /** A new view was created with `createEmbeddedView`. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"INSERTED\"] = 1] = \"INSERTED\";\n  /** The position of a view changed, but the content remains the same. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"MOVED\"] = 2] = \"MOVED\";\n  /** A view was detached from the view container. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"REMOVED\"] = 3] = \"REMOVED\";\n})(_ViewRepeaterOperation || (_ViewRepeaterOperation = {}));\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n  /**\n   * The size of the cache used to store unused views.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n   */\n  viewCacheSize = 20;\n  /**\n   * View cache that stores embedded view instances that have been previously stamped out,\n   * but don't are not currently rendered. The view repeater will reuse these views rather than\n   * creating brand new ones.\n   *\n   * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n   */\n  _viewCache = [];\n  /** Apply changes to the DOM. */\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    // Rearrange the views to put them in the right location.\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        // Item added.\n        const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n      } else if (currentIndex == null) {\n        // Item removed.\n        this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n        operation = _ViewRepeaterOperation.REMOVED;\n      } else {\n        // Item moved.\n        view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = _ViewRepeaterOperation.MOVED;\n      }\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view?.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {\n    for (const view of this._viewCache) {\n      view.destroy();\n    }\n    this._viewCache = [];\n  }\n  /**\n   * Inserts a view for a new item, either from the cache or by creating a new\n   * one. Returns `undefined` if the item was inserted into a cached view.\n   */\n  _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n    const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n    if (cachedView) {\n      cachedView.context.$implicit = value;\n      return undefined;\n    }\n    const viewArgs = viewArgsFactory();\n    return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n  }\n  /** Detaches the view at the given index and inserts into the view cache. */\n  _detachAndCacheView(index, viewContainerRef) {\n    const detachedView = viewContainerRef.detach(index);\n    this._maybeCacheView(detachedView, viewContainerRef);\n  }\n  /** Moves view at the previous index to the current index. */\n  _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n    const view = viewContainerRef.get(adjustedPreviousIndex);\n    viewContainerRef.move(view, currentIndex);\n    view.context.$implicit = value;\n    return view;\n  }\n  /**\n   * Cache the given detached view. If the cache is full, the view will be\n   * destroyed.\n   */\n  _maybeCacheView(view, viewContainerRef) {\n    if (this._viewCache.length < this.viewCacheSize) {\n      this._viewCache.push(view);\n    } else {\n      const index = viewContainerRef.indexOf(view);\n      // The host component could remove views from the container outside of\n      // the view repeater. It's unlikely this will occur, but just in case,\n      // destroy the view on its own, otherwise destroy it through the\n      // container to ensure that all the references are removed.\n      if (index === -1) {\n        view.destroy();\n      } else {\n        viewContainerRef.remove(index);\n      }\n    }\n  }\n  /** Inserts a recycled view from the cache at the given index. */\n  _insertViewFromCache(index, viewContainerRef) {\n    const cachedView = this._viewCache.pop();\n    if (cachedView) {\n      viewContainerRef.insert(cachedView, index);\n    }\n    return cachedView || null;\n  }\n}\nexport { ArrayDataSource as A, _RecycleViewRepeaterStrategy as _, _ViewRepeaterOperation as a, _VIEW_REPEATER_STRATEGY as b };\n"], "mappings": ";;;;;;;;;;;;AACA,IAAM,aAAN,MAAiB;AAAC;AAElB,SAAS,aAAa,OAAO;AAK3B,SAAO,SAAS,OAAO,MAAM,YAAY,cAAc,EAAE,iBAAiB;AAC5E;;;ACJA,IAAM,kBAAN,cAA8B,WAAW;AAAA,EACvC;AAAA,EACA,YAAY,OAAO;AACjB,UAAM;AACN,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU;AACR,WAAO,aAAa,KAAK,KAAK,IAAI,KAAK,QAAQ,GAAG,KAAK,KAAK;AAAA,EAC9D;AAAA,EACA,aAAa;AAAA,EAAC;AAChB;AAGA,IAAI;AAAA,CACH,SAAUA,yBAAwB;AAEjC,EAAAA,wBAAuBA,wBAAuB,UAAU,IAAI,CAAC,IAAI;AAEjE,EAAAA,wBAAuBA,wBAAuB,UAAU,IAAI,CAAC,IAAI;AAEjE,EAAAA,wBAAuBA,wBAAuB,OAAO,IAAI,CAAC,IAAI;AAE9D,EAAAA,wBAAuBA,wBAAuB,SAAS,IAAI,CAAC,IAAI;AAClE,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAK1D,IAAM,0BAA0B,IAAI,eAAe,eAAe;AAalE,IAAM,+BAAN,MAAmC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhB,aAAa,CAAC;AAAA;AAAA,EAEd,aAAa,SAAS,kBAAkB,oBAAoB,mBAAmB,iBAAiB;AAE9F,YAAQ,iBAAiB,CAAC,QAAQ,uBAAuB,iBAAiB;AACxE,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,iBAAiB,MAAM;AAEhC,cAAM,kBAAkB,MAAM,mBAAmB,QAAQ,uBAAuB,YAAY;AAC5F,eAAO,KAAK,YAAY,iBAAiB,cAAc,kBAAkB,kBAAkB,MAAM,CAAC;AAClG,oBAAY,OAAO,uBAAuB,WAAW,uBAAuB;AAAA,MAC9E,WAAW,gBAAgB,MAAM;AAE/B,aAAK,oBAAoB,uBAAuB,gBAAgB;AAChE,oBAAY,uBAAuB;AAAA,MACrC,OAAO;AAEL,eAAO,KAAK,UAAU,uBAAuB,cAAc,kBAAkB,kBAAkB,MAAM,CAAC;AACtG,oBAAY,uBAAuB;AAAA,MACrC;AACA,UAAI,iBAAiB;AACnB,wBAAgB;AAAA,UACd,SAAS,MAAM;AAAA,UACf;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,eAAW,QAAQ,KAAK,YAAY;AAClC,WAAK,QAAQ;AAAA,IACf;AACA,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,iBAAiB,cAAc,kBAAkB,OAAO;AAClE,UAAM,aAAa,KAAK,qBAAqB,cAAc,gBAAgB;AAC3E,QAAI,YAAY;AACd,iBAAW,QAAQ,YAAY;AAC/B,aAAO;AAAA,IACT;AACA,UAAM,WAAW,gBAAgB;AACjC,WAAO,iBAAiB,mBAAmB,SAAS,aAAa,SAAS,SAAS,SAAS,KAAK;AAAA,EACnG;AAAA;AAAA,EAEA,oBAAoB,OAAO,kBAAkB;AAC3C,UAAM,eAAe,iBAAiB,OAAO,KAAK;AAClD,SAAK,gBAAgB,cAAc,gBAAgB;AAAA,EACrD;AAAA;AAAA,EAEA,UAAU,uBAAuB,cAAc,kBAAkB,OAAO;AACtE,UAAM,OAAO,iBAAiB,IAAI,qBAAqB;AACvD,qBAAiB,KAAK,MAAM,YAAY;AACxC,SAAK,QAAQ,YAAY;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM,kBAAkB;AACtC,QAAI,KAAK,WAAW,SAAS,KAAK,eAAe;AAC/C,WAAK,WAAW,KAAK,IAAI;AAAA,IAC3B,OAAO;AACL,YAAM,QAAQ,iBAAiB,QAAQ,IAAI;AAK3C,UAAI,UAAU,IAAI;AAChB,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,yBAAiB,OAAO,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB,OAAO,kBAAkB;AAC5C,UAAM,aAAa,KAAK,WAAW,IAAI;AACvC,QAAI,YAAY;AACd,uBAAiB,OAAO,YAAY,KAAK;AAAA,IAC3C;AACA,WAAO,cAAc;AAAA,EACvB;AACF;", "names": ["_ViewRepeaterOperation"]}