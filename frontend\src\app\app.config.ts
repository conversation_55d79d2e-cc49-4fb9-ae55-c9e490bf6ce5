import { APP_INITIALIZER, ApplicationConfig } from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';
import { KeycloakService } from 'keycloak-angular';

function initializeKeycloak(keycloak: KeycloakService) {
  return () =>
    new Promise<boolean>((resolve, reject) => {
      try {
        if (typeof window === 'undefined') {
          resolve(true);
          return;
        }

        const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
        
        // Si on est sur la page register, ne pas initialiser Keycloak
        if (pathname === '/register') {
          resolve(true);
          return;
        }

        keycloak
          .init({
            config: {
              url: 'http://localhost:8080',
              realm: 'RepasKeycloak',
              clientId: 'repas-service'
            },
            initOptions: {
              onLoad: 'check-sso',
              checkLoginIframe: false,
              silentCheckSsoRedirectUri: 
                typeof window !== 'undefined' 
                  ? window.location.origin + '/assets/silent-check-sso.html'
                  : '',
            }
          })
          .then(() => {
            resolve(true);
          })
          .catch((error) => {
            console.error('Error initializing Keycloak:', error);
            reject(error);
          });
      } catch (error) {
        console.error('Error in Keycloak initialization:', error);
        reject(error);
      }
    });
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes), 
    provideClientHydration(),
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [KeycloakService],
    },
    KeycloakService
  ]
};