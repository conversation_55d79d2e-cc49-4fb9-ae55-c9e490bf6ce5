import {
  APP_BASE_HREF,
  AsyncPipe,
  BrowserPlatformLocation,
  CommonModule,
  CurrencyPipe,
  DATE_PIPE_DEFAULT_OPTIONS,
  DATE_PIPE_DEFAULT_TIMEZONE,
  DatePipe,
  DecimalPipe,
  DomAdapter,
  FormStyle,
  FormatWidth,
  HashLocationStrategy,
  I18nPluralPipe,
  I18nSelectPipe,
  IMAGE_LOADER,
  JsonPipe,
  KeyValuePipe,
  LOCATION_INITIALIZED,
  Location,
  LocationStrategy,
  LowerCasePipe,
  NgClass,
  NgComponentOutlet,
  NgForOf,
  NgForOfContext,
  NgIf,
  NgIfContext,
  NgLocaleLocalization,
  NgLocalization,
  NgOptimizedImage,
  NgPlural,
  NgPluralCase,
  NgStyle,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
  NgTemplateOutlet,
  NullViewportScroller,
  NumberFormatStyle,
  NumberSymbol,
  PRECONNECT_CHECK_BLOCKLIST,
  PathLocationStrategy,
  PercentPipe,
  PlatformLocation,
  PlatformNavigation,
  Plural,
  SlicePipe,
  TitleCasePipe,
  TranslationWidth,
  UpperCasePipe,
  VERSION,
  ViewportScroller,
  WeekDay,
  formatCurrency,
  formatDate,
  formatNumber,
  formatPercent,
  getCurrencySymbol,
  getDOM,
  getLocaleCurrencyCode,
  getLocaleCurrencyName,
  getLocaleCurrencySymbol,
  getLocaleDateFormat,
  getLocaleDateTimeFormat,
  getLocaleDayNames,
  getLocaleDayPeriods,
  getLocaleDirection,
  getLocaleEraNames,
  getLocaleExtraDayPeriodRules,
  getLocaleExtraDayPeriods,
  getLocaleFirstDayOfWeek,
  getLocaleId,
  getLocaleMonthNames,
  getLocaleNumberFormat,
  getLocaleNumberSymbol,
  getLocalePluralCase,
  getLocaleTimeFormat,
  getLocaleWeekEndRange,
  getNumberOfCurrencyDigits,
  normalizeQueryParams,
  provideCloudflareLoader,
  provideCloudinaryLoader,
  provideImageKitLoader,
  provideImgixLoader,
  provideNetlifyLoader,
  registerLocaleData,
  setRootDomAdapter
} from "./chunk-J3QXFMGW.js";
import {
  DOCUMENT,
  PLATFORM_BROWSER_ID,
  PLATFORM_SERVER_ID,
  XhrFactory,
  isPlatformBrowser,
  isPlatformServer,
  parseCookieValue
} from "./chunk-PHO26LGY.js";
import {
  IMAGE_CONFIG
} from "./chunk-6SZJNWHU.js";
import "./chunk-6Q4RANH6.js";
import "./chunk-FFZIAYYX.js";
import "./chunk-CXCX2JKZ.js";
export {
  APP_BASE_HREF,
  AsyncPipe,
  BrowserPlatformLocation,
  CommonModule,
  CurrencyPipe,
  DATE_PIPE_DEFAULT_OPTIONS,
  DATE_PIPE_DEFAULT_TIMEZONE,
  DOCUMENT,
  DatePipe,
  DecimalPipe,
  FormStyle,
  FormatWidth,
  HashLocationStrategy,
  I18nPluralPipe,
  I18nSelectPipe,
  IMAGE_CONFIG,
  IMAGE_LOADER,
  JsonPipe,
  KeyValuePipe,
  LOCATION_INITIALIZED,
  Location,
  LocationStrategy,
  LowerCasePipe,
  NgClass,
  NgComponentOutlet,
  NgForOf as NgFor,
  NgForOf,
  NgForOfContext,
  NgIf,
  NgIfContext,
  NgLocaleLocalization,
  NgLocalization,
  NgOptimizedImage,
  NgPlural,
  NgPluralCase,
  NgStyle,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
  NgTemplateOutlet,
  NumberFormatStyle,
  NumberSymbol,
  PRECONNECT_CHECK_BLOCKLIST,
  PathLocationStrategy,
  PercentPipe,
  PlatformLocation,
  Plural,
  SlicePipe,
  TitleCasePipe,
  TranslationWidth,
  UpperCasePipe,
  VERSION,
  ViewportScroller,
  WeekDay,
  XhrFactory,
  formatCurrency,
  formatDate,
  formatNumber,
  formatPercent,
  getCurrencySymbol,
  getLocaleCurrencyCode,
  getLocaleCurrencyName,
  getLocaleCurrencySymbol,
  getLocaleDateFormat,
  getLocaleDateTimeFormat,
  getLocaleDayNames,
  getLocaleDayPeriods,
  getLocaleDirection,
  getLocaleEraNames,
  getLocaleExtraDayPeriodRules,
  getLocaleExtraDayPeriods,
  getLocaleFirstDayOfWeek,
  getLocaleId,
  getLocaleMonthNames,
  getLocaleNumberFormat,
  getLocaleNumberSymbol,
  getLocalePluralCase,
  getLocaleTimeFormat,
  getLocaleWeekEndRange,
  getNumberOfCurrencyDigits,
  isPlatformBrowser,
  isPlatformServer,
  provideCloudflareLoader,
  provideCloudinaryLoader,
  provideImageKitLoader,
  provideImgixLoader,
  provideNetlifyLoader,
  registerLocaleData,
  DomAdapter as ɵDomAdapter,
  NullViewportScroller as ɵNullViewportScroller,
  PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID,
  PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID,
  PlatformNavigation as ɵPlatformNavigation,
  getDOM as ɵgetDOM,
  normalizeQueryParams as ɵnormalizeQueryParams,
  parseCookieValue as ɵparseCookieValue,
  setRootDomAdapter as ɵsetRootDomAdapter
};
