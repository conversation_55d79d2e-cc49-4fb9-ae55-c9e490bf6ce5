spring.application.name=EUREKA-SERVER

# Configuration du serveur Eureka
server.port=8761

# Configuration Eureka Server
eureka.instance.hostname=localhost
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/

# D?sactiver l'auto-pr?servation en d?veloppement
eureka.server.enable-self-preservation=false
eureka.server.eviction-interval-timer-in-ms=5000

# Configuration de s?curit?
eureka.server.expected-client-renewal-interval-seconds=30
eureka.server.renewal-percent-threshold=0.85

# Logging
logging.level.com.netflix.eureka=DEBUG
logging.level.com.netflix.discovery=DEBUG

# D?sactiver le v?rificateur de compatibilit? Spring Cloud
spring.cloud.compatibility-verifier.enabled=false

# Message de bienvenue
welcome.message=Bienvenue dans le Eureka Discovery Server!
