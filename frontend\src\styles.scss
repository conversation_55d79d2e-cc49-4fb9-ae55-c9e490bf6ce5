/* You can add global styles to this file, and also import other style files */

// Global styles
html, body {
  height: 100%;
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
  background-color: #f8f9fa;
  color: #333;
}

// Scrollbar styling
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #2196F3;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1976D2;
}

// Custom button styles
.custom-button {
  border-radius: 25px;
  padding: 8px 16px;
  border: none;
  background-color: #2196F3;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #1976D2;
    transform: translateY(-2px);
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
}

// Custom chip styles
.custom-chip {
  padding: 4px 12px;
  border-radius: 16px;
  background-color: white;
  color: #666;
  border: 1px solid #ddd;
  cursor: pointer;
  transition: all 0.3s ease;

  &.selected {
    background-color: #2196F3;
    color: white;
    border-color: #2196F3;
  }
}

// Utility classes
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

// Animations
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Card hover effect
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
}

// Container
.container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
  
  @media (min-width: 576px) {
    max-width: 540px;
  }
  
  @media (min-width: 768px) {
    max-width: 720px;
  }
  
  @media (min-width: 992px) {
    max-width: 960px;
  }
  
  @media (min-width: 1200px) {
    max-width: 1140px;
  }
  
  @media (min-width: 1400px) {
    max-width: 1320px;
  }
}

// Footer styles
app-footer {
  margin-top: auto;
  width: 100%;

  .container.my-5 {
    margin-bottom: 0 !important;
  }
}

// Utility classes
.mr-3 {
  margin-right: 1rem !important;
}

// Font Awesome icon adjustments
.fab, .fas {
  font-size: 1rem;
}
