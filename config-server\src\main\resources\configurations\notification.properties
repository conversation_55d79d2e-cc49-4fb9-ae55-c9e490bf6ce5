spring.application.name=notification-service

# Configuration Eureka
eureka.instance.hostname=localhost
eureka.client.fetch-registry=true
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${spring.application.name}:${server.port}

# Port du service
server.port=8085

# Configuration email
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=rkzn kjzi wbwz tgum
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Configuration Kafka
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=notification-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

# Topics Kafka
kafka.topics.invitation-responded=invitation.responded

# Logging
logging.level.com.example.notificationservice=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Désactiver le vérificateur de compatibilité Spring Cloud
spring.cloud.compatibility-verifier.enabled=false

# Autoriser l'écrasement des beans
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=false

# Activer les endpoints pour rafraîchir la config
management.endpoints.web.exposure.include=refresh

# Message de bienvenue
welcome.message=Bienvenue dans le Notification Service! 