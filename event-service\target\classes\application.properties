# Le nom de l'application DOIT correspondre au nom du fichier de propriétés dans le config-server
spring.application.name=EVENT

# Activer et localiser le config server
spring.config.import=configserver:http://localhost:8888

# Permettre le rafraîchissement de la configuration
management.endpoints.web.exposure.include=refresh

# Configuration Feign
feign.client.config.default.connectTimeout=5000
feign.client.config.default.readTimeout=5000
feign.client.config.default.loggerLevel=full

# Service URLs
invitation-service.url=http://localhost:8093

# Security Configuration
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/repaskeycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/repaskeycloak/protocol/openid-connect/certs

# Logging pour debug
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.example.eventservice=DEBUG
logging.level.com.example.eventservice.client=DEBUG
logging.level.feign=DEBUG
logging.level.com.example.eventservice.config=DEBUG

# API Gateway Configuration
api-gateway.url=http://localhost:8093

# Disable Hystrix for Feign
feign.hystrix.enabled=false
feign.okhttp.enabled=true
