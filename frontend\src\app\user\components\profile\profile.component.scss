/* Style pour le conteneur principal */
.profile-container {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

/* Conteneur du formulaire */
.form-container {
  background: white;
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #2196F3, #1976D2);
  }

  h2 {
    color: #2c3e50;
    font-size: 2.2em;
    margin-bottom: 40px;
    text-align: center;
    font-weight: 600;
    position: relative;
    padding-bottom: 15px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: #2196F3;
      border-radius: 3px;
    }
  }

  form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

/* Styles pour les champs de formulaire */
::ng-deep {
  .mat-mdc-form-field {
    width: 100%;

    .mat-mdc-form-field-flex {
      background-color: #f8f9fa;
      border-radius: 12px;
      padding: 0.75em 1em;
    }

    .mat-mdc-form-field-outline {
      border-radius: 12px;
    }

    .mat-mdc-text-field-wrapper {
      background-color: transparent;
    }

    .mdc-text-field--outlined {
      --mdc-outlined-text-field-container-shape: 12px;
    }

    .mat-mdc-form-field-label {
      color: #666;
    }

    &.mat-focused {
      .mat-mdc-form-field-label {
        color: #2196F3;
      }
    }
  }
}

/* Styles pour les boutons */
.button-container {
  margin-top: 30px;
  display: flex;
  justify-content: center;

  button {
    min-width: 200px;
    padding: 12px 24px;
    font-size: 1.1em;
    font-weight: 500;
    letter-spacing: 0.5px;
    border-radius: 30px;
    background: linear-gradient(45deg, #2196F3, #1976D2);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);

    &:hover:not([disabled]) {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
    }

    &:disabled {
      background: #e0e0e0;
      box-shadow: none;
    }

    mat-spinner {
      display: inline-block;
      margin-right: 8px;
    }
  }
}

/* Animation de chargement */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;

  ::ng-deep .mat-mdc-progress-spinner {
    circle {
      stroke: #2196F3;
    }
  }
}

/* Animation d'apparition */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-container {
  animation: fadeInUp 0.6s ease-out;
}

/* Styles responsives */
@media (max-width: 768px) {
  .profile-container {
    padding: 20px;
  }

  .form-container {
    padding: 30px 20px;

    h2 {
      font-size: 1.8em;
      margin-bottom: 30px;
    }
  }

  .button-container button {
    width: 100%;
  }
}

/* Styles pour les messages d'erreur */
mat-error {
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.5rem;
}

mat-hint {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}