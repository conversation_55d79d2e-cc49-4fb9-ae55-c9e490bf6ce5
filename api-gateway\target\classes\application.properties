server.port=8093

spring.application.name=api-gateway
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.instance.prefer-ip-address=true

# Configuration du Config Server
spring.cloud.config.enabled=true
spring.config.import=optional:configserver:http://localhost:8888

# Permettre la surcharge des beans
spring.main.allow-bean-definition-overriding=true

# Configuration CORS
spring.cloud.gateway.globalcors.corsConfigurations.[/**].allowedOrigins=http://localhost:4200
spring.cloud.gateway.globalcors.corsConfigurations.[/**].allowedMethods=GET,POST,PUT,DELETE,PATCH,OPTIONS
spring.cloud.gateway.globalcors.corsConfigurations.[/**].allowedHeaders=*
spring.cloud.gateway.globalcors.corsConfigurations.[/**].exposedHeaders=Authorization
spring.cloud.gateway.globalcors.corsConfigurations.[/**].allowCredentials=true
spring.cloud.gateway.globalcors.corsConfigurations.[/**].maxAge=3600

# Mode reactif pour Spring Cloud Gateway
spring.main.web-application-type=reactive

# Configuration JWT
jwt.secret-key=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970

# Gateway Routes
# Event Service Route
spring.cloud.gateway.routes[0].id=event-service
spring.cloud.gateway.routes[0].uri=lb://EVENT
spring.cloud.gateway.routes[0].predicates[0]=Path=/events/**
spring.cloud.gateway.routes[0].filters[0]=TokenRelay=
spring.cloud.gateway.routes[0].order=1

# Invitation Service Route
spring.cloud.gateway.routes[1].id=invitation-service
spring.cloud.gateway.routes[1].uri=lb://INVITATION-SERVICE
spring.cloud.gateway.routes[1].predicates[0]=Path=/invitations/**
spring.cloud.gateway.routes[1].filters[0]=TokenRelay=
spring.cloud.gateway.routes[1].order=2

# User Service Route
spring.cloud.gateway.routes[2].id=user-service
spring.cloud.gateway.routes[2].uri=lb://USER
spring.cloud.gateway.routes[2].predicates[0]=Path=/api/users/**, /auth/keycloak/**
spring.cloud.gateway.routes[2].filters[0]=TokenRelay=
spring.cloud.gateway.routes[2].order=3

# Keycloak Route
spring.cloud.gateway.routes[3].id=keycloak
spring.cloud.gateway.routes[3].uri=http://localhost:8080
spring.cloud.gateway.routes[3].predicates[0]=Path=/realms/**
spring.cloud.gateway.routes[3].order=4

# Security Configuration
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/repaskeycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/repaskeycloak/protocol/openid-connect/certs

# Logging
logging.level.org.springframework.cloud.gateway=DEBUG
logging.level.reactor.netty=DEBUG
logging.level.org.springframework.web.reactive=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.cloud.gateway.filter.TokenRelayGatewayFilterFactory=DEBUG
logging.level.org.springframework.cloud.gateway.handler.RoutePredicateHandlerMapping=DEBUG
