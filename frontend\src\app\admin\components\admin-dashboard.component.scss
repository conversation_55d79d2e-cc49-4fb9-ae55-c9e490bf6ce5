@use 'sass:color';

// Variables
$primary-color: #2196f3;
$secondary-color: #1565c0;
$success-color: #4caf50;
$danger-color: #f44336;
$warning-color: #ff9800;
$text-primary: #2c3e50;
$text-secondary: #607d8b;
$background-light: #f5f6fa;
$background-dark: #1a2035;
$sidebar-width: 260px;
$sidebar-collapsed-width: 70px;
$transition-speed: 0.3s;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow $transition-speed ease;
  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }
}

// Main Layout
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background: $background-light;
}

// Sidebar Styles
.sidebar {
  width: $sidebar-width;
  background: $background-dark;
  color: white;
  transition: width $transition-speed ease;
  position: fixed;
  height: 100vh;
  z-index: 1000;

  &.collapsed {
    width: $sidebar-collapsed-width;
    .menu-item span {
      display: none;
    }
    .logo-container h3 {
      display: none;
    }
  }

  .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .logo-container {
      @include flex-center;
      gap: 15px;

      i {
        font-size: 24px;
        color: $primary-color;
      }

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }
    }
  }

  .sidebar-menu {
    padding: 20px 0;

    .menu-item {
      padding: 15px 20px;
      display: flex;
      align-items: center;
      gap: 15px;
      cursor: pointer;
      position: relative;
      transition: all $transition-speed ease;

      i {
        font-size: 18px;
        min-width: 25px;
      }

      span {
        font-size: 15px;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      &.active {
        background: $primary-color;
        
        .menu-indicator {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background: white;
          border-radius: 2px;
        }
      }

      &.logout {
        margin-top: auto;
        color: $danger-color;
      }
    }
  }
}

// Main Content
.main-content {
  flex: 1;
  margin-left: $sidebar-width;
  padding: 30px;
  transition: margin $transition-speed ease;

  &.expanded {
    margin-left: $sidebar-collapsed-width;
  }
}

// Header Styles
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  @include card-shadow;

  .header-left {
    h2 {
      margin: 0;
      color: $text-primary;
      font-size: 24px;
    }

    .subtitle {
      color: $text-secondary;
      margin: 5px 0 0;
    }
  }

  .header-actions {
    display: flex;
    gap: 15px;
    align-items: center;

    .search-box {
      position: relative;
      
      input {
        padding: 10px 15px 10px 40px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        width: 300px;
        font-size: 14px;
        transition: all $transition-speed ease;

        &:focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
          outline: none;
        }
      }

      i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: $text-secondary;
      }
    }

    button {
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 10px;
      font-weight: 500;
      transition: all $transition-speed ease;

      &.refresh-btn {
        background: $background-light;
        color: $text-secondary;

        &:hover {
          background: color.adjust($background-light, $lightness: -5%);
        }
      }

      &.add-btn {
        background: $primary-color;
        color: white;

        &:hover {
          background: $secondary-color;
        }
      }
    }
  }
}

// Stats Cards
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    @include card-shadow;

    .stat-icon {
      @include flex-center;
      width: 60px;
      height: 60px;
      border-radius: 12px;
      font-size: 24px;

      &.total { background: rgba($primary-color, 0.1); color: $primary-color; }
      &.active { background: rgba($success-color, 0.1); color: $success-color; }
      &.inactive { background: rgba($danger-color, 0.1); color: $danger-color; }
    }

    .stat-info {
      h3 {
        margin: 0;
        font-size: 28px;
        color: $text-primary;
      }

      p {
        margin: 5px 0;
        color: $text-secondary;
      }

      .trend {
        font-size: 12px;
        font-weight: 500;

        &.up { color: $success-color; }
        &.down { color: $danger-color; }
      }
    }
  }
}

// Table Styles
.table-container {
  background: white;
  border-radius: 10px;
  padding: 20px;
  @include card-shadow;
  overflow-x: auto;

  table {
    width: 100%;
    border-collapse: collapse;

    th, td {
      padding: 15px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    th {
      color: $text-secondary;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 12px;
      letter-spacing: 0.5px;
    }

    td {
      color: $text-primary;
      font-size: 14px;

      .status {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;

        &.active {
          background: rgba($success-color, 0.1);
          color: $success-color;
        }

        &.inactive {
          background: rgba($danger-color, 0.1);
          color: $danger-color;
        }
      }
    }

    .actions {
      display: flex;
      gap: 10px;

      .action-btn {
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
        border-radius: 5px;
        transition: all $transition-speed ease;

        i {
          font-size: 16px;
        }

        &:hover {
          background: $background-light;
        }

        &.edit i { color: $warning-color; }
        &.delete i { color: $danger-color; }
      }
    }
  }
}

// Form Styles
.event-form {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
  @include card-shadow;

  h3 {
    margin: 0 0 20px;
    color: $text-primary;
  }

  .form-group {
    margin-bottom: 15px;

    input, textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      font-size: 14px;
      transition: all $transition-speed ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
        outline: none;
      }
    }

    textarea {
      min-height: 100px;
      resize: vertical;
    }
  }

  .create-btn {
    background: $primary-color;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all $transition-speed ease;

    &:hover {
      background: $secondary-color;
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .sidebar {
    width: $sidebar-collapsed-width;
    
    .menu-item span,
    .logo-container h3 {
      display: none;
    }
  }

  .main-content {
    margin-left: $sidebar-collapsed-width;
  }

  .content-header {
    flex-direction: column;
    gap: 20px;

    .header-actions {
      width: 100%;
      
      .search-box {
        flex: 1;
        
        input {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }

  .table-container {
    overflow-x: auto;
    
    table {
      min-width: 800px;
    }
  }
}

.confirm-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #0056b3;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
}

.confirmed-button {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: default;
}

.delete-btn {
  background-color: transparent;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 8px;
  transition: color 0.3s;

  &:hover {
    color: #c82333;
  }
} 