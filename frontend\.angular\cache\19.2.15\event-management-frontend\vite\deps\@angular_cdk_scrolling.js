import {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollable,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  DEFAULT_RESIZE_TIME,
  DEFAULT_SCROLL_TIME,
  FixedSizeVirtualScrollStrategy,
  ScrollDispatcher,
  ScrollingModule,
  VIRTUAL_SCROLLABLE,
  VIRTUAL_SCROLL_STRATEGY,
  ViewportRuler,
  _fixedSizeVirtualScrollStrategyFactory
} from "./chunk-LD2XRELH.js";
import "./chunk-Q67IHAQN.js";
import "./chunk-2O4WY5GE.js";
import {
  Dir
} from "./chunk-3CEG45YH.js";
import "./chunk-3XD6ZZNQ.js";
import "./chunk-FRRLOGC4.js";
import "./chunk-J3QXFMGW.js";
import "./chunk-PHO26LGY.js";
import "./chunk-6SZJNWHU.js";
import "./chunk-6Q4RANH6.js";
import "./chunk-FFZIAYYX.js";
import "./chunk-CXCX2JKZ.js";
export {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollable,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  DEFAULT_RESIZE_TIME,
  DEFAULT_SCROLL_TIME,
  FixedSizeVirtualScrollStrategy,
  ScrollDispatcher,
  ScrollingModule,
  VIRTUAL_SCROLLABLE,
  VIRTUAL_SCROLL_STRATEGY,
  ViewportRuler,
  _fixedSizeVirtualScrollStrategyFactory,
  Dir as ɵɵDir
};
