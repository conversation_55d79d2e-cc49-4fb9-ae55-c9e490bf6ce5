<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="netflix-candidates" />
      <option name="name" value="Netflix Candidates" />
      <option name="url" value="https://artifactory-&#10;                oss.prod.netflix.net/artifactory/maven-oss-candidates" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="netflix-candidates" />
      <option name="name" value="Netflix Candidates" />
      <option name="url" value="https://artifactory-oss.prod.netflix.net/artifactory/maven-oss-candidates" />
    </remote-repository>
  </component>
</project>