// Variables
$primary-color: #3f51b5;
$warn-color: #f44336;
$background-color: #f5f7fa;
$card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
$border-radius: 8px;

// Styles du conteneur principal
.home-container {
  min-height: 100vh;
  background-color: $background-color;
  padding: 2rem;
  position: relative;
}

// En-tête
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  h1 {
    color: #333;
    font-size: 2rem;
    font-weight: 500;
    margin: 0;
  }
  
  button {
    mat-icon {
      margin-right: 8px;
    }
  }
}

// Contenu principal
.content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  
  @media (min-width: 960px) {
    grid-template-columns: 2fr 1fr;
  }
}

// Carte de bienvenue
.welcome-card {
  grid-column: 1 / -1;
  border-radius: $border-radius;
  overflow: hidden;
  box-shadow: $card-shadow;
  
  mat-card-header {
    background-color: $primary-color;
    color: white;
    padding: 1.5rem;
    
    .user-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      width: 60px;
      height: 60px;
      
      mat-icon {
        font-size: 40px;
        width: 40px;
        height: 40px;
      }
    }
    
    mat-card-title {
      color: white;
      font-size: 1.5rem;
      margin: 0.5rem 0 0;
    }
    
    mat-card-subtitle {
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  mat-card-content {
    padding: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #555;
  }
}

// Carte de profil
.profile-card {
  border-radius: $border-radius;
  overflow: hidden;
  box-shadow: $card-shadow;
  height: fit-content;
  
  mat-card-header {
    background-color: #f9f9f9;
    padding: 1.5rem 1.5rem 0.5rem;
    
    mat-card-title {
      display: flex;
      align-items: center;
      color: $primary-color;
      font-size: 1.2rem;
      
      mat-icon {
        margin-right: 8px;
        color: $primary-color;
      }
    }
  }
  
  mat-card-content {
    padding: 1rem 0;
    
    mat-list {
      padding: 0;
      
      mat-list-item {
        height: auto;
        padding: 0.75rem 1.5rem;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.02);
        }
        
        span[matListItemTitle] {
          font-weight: 500;
          color: #555;
          font-size: 0.9rem;
        }
        
        span[matLine] {
          color: #666;
          font-size: 1rem;
          margin-top: 4px;
          
          mat-chip-listbox {
            margin-top: 8px;
            
            mat-chip {
              margin-right: 8px;
              margin-bottom: 8px;
            }
          }
        }
      }
      
      mat-divider {
        margin: 0.5rem 1.5rem;
      }
    }
  }
}

// Grille des fonctionnalités
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
  
  @media (min-width: 600px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 960px) {
    grid-column: 1 / 2;
    margin-top: 0;
  }
}

// Carte de fonctionnalité
.feature-card {
  border-radius: $border-radius;
  overflow: hidden;
  box-shadow: $card-shadow;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }
  
  mat-card-header {
    padding: 1.5rem 1.5rem 0.5rem;
    
    mat-card-title {
      display: flex;
      align-items: center;
      color: $primary-color;
      font-size: 1.1rem;
      margin: 0;
      
      mat-icon {
        margin-right: 8px;
        color: $primary-color;
      }
    }
  }
  
  mat-card-content {
    padding: 0.5rem 1.5rem 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    
    p {
      color: #666;
      margin: 0 0 1rem;
      line-height: 1.5;
    }
    
    button {
      margin-top: auto;
      align-self: flex-start;
      
      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        margin-left: 4px;
      }
    }
  }
}

// Overlay de chargement
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  
  p {
    margin-top: 1rem;
    color: #555;
    font-size: 1.1rem;
  }
}

// Styles responsifs
@media (max-width: 959px) {
  .home-container {
    padding: 1.5rem 1rem;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    
    h1 {
      font-size: 1.75rem;
    }
    
    button {
      width: 100%;
    }
  }
  
  .welcome-card {
    mat-card-content {
      padding: 1.25rem;
    }
  }
  
  .profile-card {
    mat-card-content mat-list {
      mat-list-item {
        padding: 0.75rem 1.25rem;
      }
      
      mat-divider {
        margin: 0.5rem 1.25rem;
      }
    }
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}

// Styles pour les écrans très petits
@media (max-width: 479px) {
  .home-container {
    padding: 1rem 0.75rem;
  }
  
  .header {
    h1 {
      font-size: 1.5rem;
    }
  }
  
  .welcome-card,
  .profile-card,
  .feature-card {
    border-radius: 0;
    box-shadow: none;
    border: 1px solid #eee;
  }
  
  .profile-card {
    mat-card-content mat-list {
      mat-list-item {
        padding: 0.75rem 1rem;
      }
      
      mat-divider {
        margin: 0.5rem 1rem;
      }
    }
  }
}

// Hero Section
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  background-image: url('/assets/images/hero-bg.jpg');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.6), rgba(0,0,0,0.3));
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  padding: 0 20px;

  h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  }

  p {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  }
}

.hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;

  button {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 30px;
    border: none;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }
  }

  .btn-primary {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
  }

  .btn-secondary {
    background: rgba(255,255,255,0.9);
    color: #333;
  }
}

// Features Section
.features-section {
  padding: 100px 0;
  background: #f8f9fa;

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #333;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background: #2196F3;
    }
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  padding: 0 20px;
}

.feature-card {
  background: white;
  padding: 40px 30px;
  border-radius: 15px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
  }

  .feature-icon {
    font-size: 2.5rem;
    color: #2196F3;
    margin-bottom: 20px;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #333;
  }

  p {
    color: #666;
    line-height: 1.6;
  }
}

// Events Section
.events-section {
  padding: 80px 0;
  background-color: #f8f9fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    text-align: center;
    margin-bottom: 50px;
    color: #2c3e50;
    font-size: 2.5em;
    font-weight: 600;

    &::after {
      content: '';
      display: block;
      width: 60px;
      height: 3px;
      background: #2196F3;
      margin: 20px auto 0;
      border-radius: 3px;
    }
  }

  .events-slider {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    padding: 20px 0;
  }

  .event-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }

    .event-image {
      position: relative;
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.1);
      }

      .event-date {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(33, 150, 243, 0.9);
        color: white;
        padding: 10px 15px;
        border-radius: 10px;
        text-align: center;
        backdrop-filter: blur(5px);

        .day {
          display: block;
          font-size: 1.8em;
          font-weight: 700;
          line-height: 1;
        }

        .month {
          display: block;
          font-size: 0.9em;
          text-transform: uppercase;
          margin-top: 3px;
        }
      }
    }

    .event-content {
      padding: 25px;

      h3 {
        color: #2c3e50;
        font-size: 1.4em;
        margin: 0 0 15px 0;
        font-weight: 600;
      }

      .event-location {
        color: #666;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #2196F3;
        }
      }

      .event-description {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .btn-details {
        background: linear-gradient(45deg, #2196F3, #1976D2);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);

        &:hover {
          background: linear-gradient(45deg, #1976D2, #1565C0);
          box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .events-section {
    padding: 40px 0;

    .section-title {
      font-size: 2em;
    }

    .events-slider {
      grid-template-columns: 1fr;
      padding: 10px;
    }

    .event-card {
      .event-image {
        height: 180px;
      }

      .event-content {
        padding: 20px;

        h3 {
          font-size: 1.2em;
        }
      }
    }
  }
}

// Statistics Section
.stats-section {
  padding: 80px 0;
  background: linear-gradient(45deg, #1976D2, #2196F3);
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  text-align: center;
}

.stat-item {
  .stat-number {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
  }
}

// Call to Action Section
.cta-section {
  padding: 100px 0;
  background: url('/assets/images/cta-bg.jpg') center/cover;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.6);
  }
}

.cta-content {
  position: relative;
  color: white;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
  }

  .btn-cta {
    background: #2196F3;
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    border-radius: 30px;
    cursor: pointer;
    transition: transform 0.3s ease, background 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      background: #1976D2;
    }
  }
}

// Newsletter Section
.newsletter-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.newsletter-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;

  h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #333;
  }

  p {
    color: #666;
    margin-bottom: 30px;
  }
}

.newsletter-form {
  display: flex;
  gap: 10px;
  max-width: 500px;
  margin: 0 auto;

  input {
    flex: 1;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 25px;
    font-size: 1rem;

    &:focus {
      outline: none;
      border-color: #2196F3;
    }
  }

  .btn-subscribe {
    background: #2196F3;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    transition: background 0.3s ease;

    &:hover {
      background: #1976D2;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .hero-section {
    min-height: 500px;

    h1 {
      font-size: 2.5rem;
    }

    p {
      font-size: 1.2rem;
    }
  }

  .hero-buttons {
    flex-direction: column;
    gap: 15px;

    button {
      width: 100%;
    }
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }

  .newsletter-form {
    flex-direction: column;

    .btn-subscribe {
      width: 100%;
    }
  }
}
