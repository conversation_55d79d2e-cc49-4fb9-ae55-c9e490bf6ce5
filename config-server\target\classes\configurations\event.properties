spring.application.name=EVENT

# Configuration PostgreSQL
spring.datasource.url=****************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# Pool de connexions HikariCP
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=600000
spring.datasource.hikari.connection-timeout=30000

# Configuration JPA/Hibernate pour PostgreSQL
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Optimisations PostgreSQL
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Configuration Eureka
eureka.instance.hostname=localhost
eureka.client.fetch-registry=true
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${spring.application.name}:${server.port}
eureka.instance.appname=${spring.application.name}

# Port du service
server.port=8082

# Configuration Kafka
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
spring.kafka.producer.properties.spring.json.add.type.headers=false

# Topics Kafka
kafka.topics.event-created=event.created
kafka.topics.event-updated=event.updated

# Logging pour debug
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.kafka=DEBUG

# Désactiver le vérificateur de compatibilité Spring Cloud
spring.cloud.compatibility-verifier.enabled=false

# Autoriser l'écrasement des beans
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=false

# Activer les endpoints pour rafraîchir la config
management.endpoints.web.exposure.include=refresh

# Message de bienvenue
welcome.message=Bienvenue dans le Event Service!

# Configuration Keycloak
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/RepasKeycloak

# JWT Configuration
jwt.secret-key=NouveauSecretPourJwtHs256QuiEstAssezLongEtComplexePourEtreSecuriseEnBase64=

# API Gateway Configuration
api-gateway.url=http://localhost:8093
