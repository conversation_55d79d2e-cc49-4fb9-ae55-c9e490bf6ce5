spring.application.name=config-server
server.port=8888

# Activation du mode "native" pour les fichiers locaux
spring.profiles.active=native
spring.cloud.config.server.native.searchLocations=classpath:/configurations

# Configuration Eureka
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
