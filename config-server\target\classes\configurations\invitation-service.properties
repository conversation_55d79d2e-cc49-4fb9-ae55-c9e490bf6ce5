spring.application.name=INVITATION-SERVICE

# Database Configuration
spring.datasource.url=*********************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# Configuration JPA/Hibernate pour PostgreSQL
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Configuration Eureka
eureka.instance.hostname=localhost
eureka.client.fetch-registry=true
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${spring.application.name}:${server.port}
eureka.instance.appname=${spring.application.name}

# Port du service
server.port=8083

# Configuration Kafka
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.properties.spring.json.add.type.headers=false

# Topics Kafka
kafka.topics.invitation-responded=invitation.responded

# Désactiver le vérificateur de compatibilité Spring Cloud
spring.cloud.compatibility-verifier.enabled=false

# Autoriser l'écrasement des beans
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=false

# Activer les endpoints pour rafraîchir la config
management.endpoints.web.exposure.include=refresh

# Message de bienvenue
welcome.message=Bienvenue dans le Invitation Service!

# Configuration OAuth2/JWT
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/RepasKeycloak/protocol/openid-connect/certs

# Configuration du token relay
spring.security.oauth2.client.provider.keycloak.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.client.registration.keycloak.client-id=repas-service
spring.security.oauth2.client.registration.keycloak.client-secret=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.keycloak.scope=openid,profile,email,roles

# Logging pour debug
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.example.invitationservice=DEBUG
logging.level.feign=DEBUG

# Configuration du serveur
server.port=8083

# Configuration Eureka
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.instance.prefer-ip-address=true 