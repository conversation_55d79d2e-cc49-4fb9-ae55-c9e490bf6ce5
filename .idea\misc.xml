<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/ApiGateway/pom.xml" />
        <option value="$PROJECT_DIR$/config-server/pom.xml" />
        <option value="$PROJECT_DIR$/serverdiscover/pom.xml" />
        <option value="$PROJECT_DIR$/projetmicroservicesrepasfiras/pom.xml" />
        <option value="$PROJECT_DIR$/microservice-Supplier/pom.xml" />
        <option value="$PROJECT_DIR$/USER/pom.xml" />
        <option value="$PROJECT_DIR$/event-service/pom.xml" />
        <option value="$PROJECT_DIR$/invitation-service/pom.xml" />
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/api-gateway/pom.xml" />
        <option value="$PROJECT_DIR$/notification-service/pom.xml" />
        <option value="$PROJECT_DIR$/keycloak-event-listener/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/USER/pom.xml" />
        <option value="$PROJECT_DIR$/microservice-Supplier/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="jbr-17" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>