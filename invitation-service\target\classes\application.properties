# Nom de l'application
spring.application.name=INVITATION-SERVICE

# Configuration du Config Server
spring.config.import=optional:configserver:http://localhost:8888
spring.cloud.config.fail-fast=true

# Permettre le rafraîchissement de la configuration
management.endpoints.web.exposure.include=refresh

# Configuration OAuth2/JWT
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/RepasKeycloak/protocol/openid-connect/certs

# Logging pour debug
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.example.invitationservice=DEBUG
logging.level.feign=DEBUG

# Configuration du serveur
server.port=8083

# Configuration Eureka
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.instance.prefer-ip-address=true

# Configuration du token relay
spring.security.oauth2.client.provider.keycloak.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.client.registration.keycloak.client-id=repas-service
spring.security.oauth2.client.registration.keycloak.client-secret=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.keycloak.scope=openid,profile,email,roles

# Kafka Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

# Topic names
kafka.topics.invitation-responded=invitation.responded