spring.application.name=USER

# Configuration PostgreSQL optimis?e
spring.datasource.url=***************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# Pool de connexions HikariCP (optimis? pour PostgreSQL)
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=600000
spring.datasource.hikari.connection-timeout=30000

# Configuration JPA/Hibernate pour PostgreSQL
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Optimisations PostgreSQL
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Logging pour debug
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE


# Configuration Eureka
eureka.instance.hostname=localhost
eureka.client.fetch-registry=true
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${spring.application.name}:${server.port}

# Port du service
server.port=8084

# Configuration Keycloak
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/RepasKeycloak/protocol/openid-connect/certs

## Keycloak Configuration d?taill?e
#keycloak.auth-server-url=http://localhost:8080
#keycloak.realm=RepasKeycloak
#keycloak.resource=repas-service
#keycloak.credentials.secret=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq
#keycloak.ssl-required=external
#keycloak.use-resource-role-mappings=true
#keycloak.bearer-only=true
#keycloak.public-client=false

# JWT Configuration
jwt.auth.converter.resource-id=repas-service
jwt.auth.converter.principal-attribute=preferred_username
# REMPLACEZ CETTE CL? PAR LA VRAIE CL? SECR?TE DE KEYCLOAK
jwt.secret-key=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq

# Enable role conversion
keycloak.security-constraints[0].authRoles[0]=ADMIN
keycloak.security-constraints[0].securityCollections[0].patterns[0]=/api/users/*

# Google OAuth2 Configuration
google.clientId=************.apps.googleusercontent.com

# Configuration Keycloak Admin Client (Service Account)
keycloak.admin.server-url=http://localhost:8080
keycloak.admin.realm=RepasKeycloak
keycloak.admin.client-id=repas-service
keycloak.admin.client-secret=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq
keycloak.admin.grant-type=client_credentials

# Initialisation des donn?es
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always

# Logging Configuration
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.oauth2=DEBUG
logging.level.org.keycloak=DEBUG

# D?sactiver le v?rificateur de compatibilit? Spring Cloud
spring.cloud.compatibility-verifier.enabled=false

# Autoriser l'?crasement des beans
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=false

# Activer les endpoints pour rafra?chir la config
management.endpoints.web.exposure.include=refresh

# Message de bienvenue
welcome.message=Bienvenue dans le User Service!
