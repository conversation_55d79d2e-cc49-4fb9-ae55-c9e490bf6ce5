<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="config-server" />
        <module name="invitation-service" />
        <module name="keycloak-event-listener" />
        <module name="serverdiscover" />
        <module name="user-service" />
        <module name="ApiGateway" />
        <module name="notification-service" />
      </profile>
      <profile name="Annotation profile for Event Management Microservices" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="event-service" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="microservices" target="17" />
      <module name="user-service (1)" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ApiGateway" options="-parameters" />
      <module name="Fournisseur_Back" options="-parameters" />
      <module name="config-server" options="-parameters" />
      <module name="event-service" options="-parameters" />
      <module name="invitation-service" options="-parameters" />
      <module name="microservices" options="-parameters" />
      <module name="notification-service" options="-parameters" />
      <module name="serverdiscover" options="-parameters" />
      <module name="user-service" options="-parameters" />
      <module name="user-service (1)" options="-parameters" />
    </option>
  </component>
</project>