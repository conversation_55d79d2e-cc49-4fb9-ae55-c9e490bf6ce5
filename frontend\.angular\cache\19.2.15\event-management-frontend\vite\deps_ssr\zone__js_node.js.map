{"version": 3, "sources": ["../../../../../../node_modules/zone.js/fesm2015/zone-node.js"], "sourcesContent": ["'use strict';\n\n/**\n * @license Angular v<unknown>\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\nconst global = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n  const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n  return symbolPrefix + name;\n}\nfunction initZone() {\n  const performance = global['performance'];\n  function mark(name) {\n    performance && performance['mark'] && performance['mark'](name);\n  }\n  function performanceMeasure(name, label) {\n    performance && performance['measure'] && performance['measure'](name, label);\n  }\n  mark('Zone');\n  class ZoneImpl {\n    static __symbol__ = __symbol__;\n    static assertZonePatched() {\n      if (global['Promise'] !== patches['ZoneAwarePromise']) {\n        throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' + 'has been overwritten.\\n' + 'Most likely cause is that a Promise polyfill has been loaded ' + 'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' + 'If you must load one, do so before loading zone.js.)');\n      }\n    }\n    static get root() {\n      let zone = ZoneImpl.current;\n      while (zone.parent) {\n        zone = zone.parent;\n      }\n      return zone;\n    }\n    static get current() {\n      return _currentZoneFrame.zone;\n    }\n    static get currentTask() {\n      return _currentTask;\n    }\n    static __load_patch(name, fn, ignoreDuplicate = false) {\n      if (patches.hasOwnProperty(name)) {\n        // `checkDuplicate` option is defined from global variable\n        // so it works for all modules.\n        // `ignoreDuplicate` can work for the specified module\n        const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n        if (!ignoreDuplicate && checkDuplicate) {\n          throw Error('Already loaded patch: ' + name);\n        }\n      } else if (!global['__Zone_disable_' + name]) {\n        const perfName = 'Zone:' + name;\n        mark(perfName);\n        patches[name] = fn(global, ZoneImpl, _api);\n        performanceMeasure(perfName, perfName);\n      }\n    }\n    get parent() {\n      return this._parent;\n    }\n    get name() {\n      return this._name;\n    }\n    _parent;\n    _name;\n    _properties;\n    _zoneDelegate;\n    constructor(parent, zoneSpec) {\n      this._parent = parent;\n      this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n      this._properties = zoneSpec && zoneSpec.properties || {};\n      this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n    }\n    get(key) {\n      const zone = this.getZoneWith(key);\n      if (zone) return zone._properties[key];\n    }\n    getZoneWith(key) {\n      let current = this;\n      while (current) {\n        if (current._properties.hasOwnProperty(key)) {\n          return current;\n        }\n        current = current._parent;\n      }\n      return null;\n    }\n    fork(zoneSpec) {\n      if (!zoneSpec) throw new Error('ZoneSpec required!');\n      return this._zoneDelegate.fork(this, zoneSpec);\n    }\n    wrap(callback, source) {\n      if (typeof callback !== 'function') {\n        throw new Error('Expecting function got: ' + callback);\n      }\n      const _callback = this._zoneDelegate.intercept(this, callback, source);\n      const zone = this;\n      return function () {\n        return zone.runGuarded(_callback, this, arguments, source);\n      };\n    }\n    run(callback, applyThis, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n    runGuarded(callback, applyThis = null, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        try {\n          return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n    runTask(task, applyThis, applyArgs) {\n      if (task.zone != this) {\n        throw new Error('A task can only be run in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n      }\n      const zoneTask = task;\n      // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n      // will run in notScheduled(canceled) state, we should not try to\n      // run such kind of task but just return\n      const {\n        type,\n        data: {\n          isPeriodic = false,\n          isRefreshable = false\n        } = {}\n      } = task;\n      if (task.state === notScheduled && (type === eventTask || type === macroTask)) {\n        return;\n      }\n      const reEntryGuard = task.state != running;\n      reEntryGuard && zoneTask._transitionTo(running, scheduled);\n      const previousTask = _currentTask;\n      _currentTask = zoneTask;\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        if (type == macroTask && task.data && !isPeriodic && !isRefreshable) {\n          task.cancelFn = undefined;\n        }\n        try {\n          return this._zoneDelegate.invokeTask(this, zoneTask, applyThis, applyArgs);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        // if the task's state is notScheduled or unknown, then it has already been cancelled\n        // we should not reset the state to scheduled\n        const state = task.state;\n        if (state !== notScheduled && state !== unknown) {\n          if (type == eventTask || isPeriodic || isRefreshable && state === scheduling) {\n            reEntryGuard && zoneTask._transitionTo(scheduled, running, scheduling);\n          } else {\n            const zoneDelegates = zoneTask._zoneDelegates;\n            this._updateTaskCount(zoneTask, -1);\n            reEntryGuard && zoneTask._transitionTo(notScheduled, running, notScheduled);\n            if (isRefreshable) {\n              zoneTask._zoneDelegates = zoneDelegates;\n            }\n          }\n        }\n        _currentZoneFrame = _currentZoneFrame.parent;\n        _currentTask = previousTask;\n      }\n    }\n    scheduleTask(task) {\n      if (task.zone && task.zone !== this) {\n        // check if the task was rescheduled, the newZone\n        // should not be the children of the original zone\n        let newZone = this;\n        while (newZone) {\n          if (newZone === task.zone) {\n            throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n          }\n          newZone = newZone.parent;\n        }\n      }\n      task._transitionTo(scheduling, notScheduled);\n      const zoneDelegates = [];\n      task._zoneDelegates = zoneDelegates;\n      task._zone = this;\n      try {\n        task = this._zoneDelegate.scheduleTask(this, task);\n      } catch (err) {\n        // should set task's state to unknown when scheduleTask throw error\n        // because the err may from reschedule, so the fromState maybe notScheduled\n        task._transitionTo(unknown, scheduling, notScheduled);\n        // TODO: @JiaLiPassion, should we check the result from handleError?\n        this._zoneDelegate.handleError(this, err);\n        throw err;\n      }\n      if (task._zoneDelegates === zoneDelegates) {\n        // we have to check because internally the delegate can reschedule the task.\n        this._updateTaskCount(task, 1);\n      }\n      if (task.state == scheduling) {\n        task._transitionTo(scheduled, scheduling);\n      }\n      return task;\n    }\n    scheduleMicroTask(source, callback, data, customSchedule) {\n      return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n    }\n    scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n    }\n    scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n    }\n    cancelTask(task) {\n      if (task.zone != this) throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n      if (task.state !== scheduled && task.state !== running) {\n        return;\n      }\n      task._transitionTo(canceling, scheduled, running);\n      try {\n        this._zoneDelegate.cancelTask(this, task);\n      } catch (err) {\n        // if error occurs when cancelTask, transit the state to unknown\n        task._transitionTo(unknown, canceling);\n        this._zoneDelegate.handleError(this, err);\n        throw err;\n      }\n      this._updateTaskCount(task, -1);\n      task._transitionTo(notScheduled, canceling);\n      task.runCount = -1;\n      return task;\n    }\n    _updateTaskCount(task, count) {\n      const zoneDelegates = task._zoneDelegates;\n      if (count == -1) {\n        task._zoneDelegates = null;\n      }\n      for (let i = 0; i < zoneDelegates.length; i++) {\n        zoneDelegates[i]._updateTaskCount(task.type, count);\n      }\n    }\n  }\n  const DELEGATE_ZS = {\n    name: '',\n    onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n    onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n    onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n    onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n  };\n  class _ZoneDelegate {\n    get zone() {\n      return this._zone;\n    }\n    _zone;\n    _taskCounts = {\n      'microTask': 0,\n      'macroTask': 0,\n      'eventTask': 0\n    };\n    _parentDelegate;\n    _forkDlgt;\n    _forkZS;\n    _forkCurrZone;\n    _interceptDlgt;\n    _interceptZS;\n    _interceptCurrZone;\n    _invokeDlgt;\n    _invokeZS;\n    _invokeCurrZone;\n    _handleErrorDlgt;\n    _handleErrorZS;\n    _handleErrorCurrZone;\n    _scheduleTaskDlgt;\n    _scheduleTaskZS;\n    _scheduleTaskCurrZone;\n    _invokeTaskDlgt;\n    _invokeTaskZS;\n    _invokeTaskCurrZone;\n    _cancelTaskDlgt;\n    _cancelTaskZS;\n    _cancelTaskCurrZone;\n    _hasTaskDlgt;\n    _hasTaskDlgtOwner;\n    _hasTaskZS;\n    _hasTaskCurrZone;\n    constructor(zone, parentDelegate, zoneSpec) {\n      this._zone = zone;\n      this._parentDelegate = parentDelegate;\n      this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n      this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n      this._forkCurrZone = zoneSpec && (zoneSpec.onFork ? this._zone : parentDelegate._forkCurrZone);\n      this._interceptZS = zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n      this._interceptDlgt = zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n      this._interceptCurrZone = zoneSpec && (zoneSpec.onIntercept ? this._zone : parentDelegate._interceptCurrZone);\n      this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n      this._invokeDlgt = zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n      this._invokeCurrZone = zoneSpec && (zoneSpec.onInvoke ? this._zone : parentDelegate._invokeCurrZone);\n      this._handleErrorZS = zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n      this._handleErrorDlgt = zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n      this._handleErrorCurrZone = zoneSpec && (zoneSpec.onHandleError ? this._zone : parentDelegate._handleErrorCurrZone);\n      this._scheduleTaskZS = zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n      this._scheduleTaskDlgt = zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n      this._scheduleTaskCurrZone = zoneSpec && (zoneSpec.onScheduleTask ? this._zone : parentDelegate._scheduleTaskCurrZone);\n      this._invokeTaskZS = zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n      this._invokeTaskDlgt = zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n      this._invokeTaskCurrZone = zoneSpec && (zoneSpec.onInvokeTask ? this._zone : parentDelegate._invokeTaskCurrZone);\n      this._cancelTaskZS = zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n      this._cancelTaskDlgt = zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n      this._cancelTaskCurrZone = zoneSpec && (zoneSpec.onCancelTask ? this._zone : parentDelegate._cancelTaskCurrZone);\n      this._hasTaskZS = null;\n      this._hasTaskDlgt = null;\n      this._hasTaskDlgtOwner = null;\n      this._hasTaskCurrZone = null;\n      const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n      const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n      if (zoneSpecHasTask || parentHasTask) {\n        // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n        // a case all task related interceptors must go through this ZD. We can't short circuit it.\n        this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n        this._hasTaskDlgt = parentDelegate;\n        this._hasTaskDlgtOwner = this;\n        this._hasTaskCurrZone = this._zone;\n        if (!zoneSpec.onScheduleTask) {\n          this._scheduleTaskZS = DELEGATE_ZS;\n          this._scheduleTaskDlgt = parentDelegate;\n          this._scheduleTaskCurrZone = this._zone;\n        }\n        if (!zoneSpec.onInvokeTask) {\n          this._invokeTaskZS = DELEGATE_ZS;\n          this._invokeTaskDlgt = parentDelegate;\n          this._invokeTaskCurrZone = this._zone;\n        }\n        if (!zoneSpec.onCancelTask) {\n          this._cancelTaskZS = DELEGATE_ZS;\n          this._cancelTaskDlgt = parentDelegate;\n          this._cancelTaskCurrZone = this._zone;\n        }\n      }\n    }\n    fork(targetZone, zoneSpec) {\n      return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) : new ZoneImpl(targetZone, zoneSpec);\n    }\n    intercept(targetZone, callback, source) {\n      return this._interceptZS ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) : callback;\n    }\n    invoke(targetZone, callback, applyThis, applyArgs, source) {\n      return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) : callback.apply(applyThis, applyArgs);\n    }\n    handleError(targetZone, error) {\n      return this._handleErrorZS ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) : true;\n    }\n    scheduleTask(targetZone, task) {\n      let returnTask = task;\n      if (this._scheduleTaskZS) {\n        if (this._hasTaskZS) {\n          returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n        }\n        returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n        if (!returnTask) returnTask = task;\n      } else {\n        if (task.scheduleFn) {\n          task.scheduleFn(task);\n        } else if (task.type == microTask) {\n          scheduleMicroTask(task);\n        } else {\n          throw new Error('Task is missing scheduleFn.');\n        }\n      }\n      return returnTask;\n    }\n    invokeTask(targetZone, task, applyThis, applyArgs) {\n      return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) : task.callback.apply(applyThis, applyArgs);\n    }\n    cancelTask(targetZone, task) {\n      let value;\n      if (this._cancelTaskZS) {\n        value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n      } else {\n        if (!task.cancelFn) {\n          throw Error('Task is not cancelable');\n        }\n        value = task.cancelFn(task);\n      }\n      return value;\n    }\n    hasTask(targetZone, isEmpty) {\n      // hasTask should not throw error so other ZoneDelegate\n      // can still trigger hasTask callback\n      try {\n        this._hasTaskZS && this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n      } catch (err) {\n        this.handleError(targetZone, err);\n      }\n    }\n    _updateTaskCount(type, count) {\n      const counts = this._taskCounts;\n      const prev = counts[type];\n      const next = counts[type] = prev + count;\n      if (next < 0) {\n        throw new Error('More tasks executed then were scheduled.');\n      }\n      if (prev == 0 || next == 0) {\n        const isEmpty = {\n          microTask: counts['microTask'] > 0,\n          macroTask: counts['macroTask'] > 0,\n          eventTask: counts['eventTask'] > 0,\n          change: type\n        };\n        this.hasTask(this._zone, isEmpty);\n      }\n    }\n  }\n  class ZoneTask {\n    type;\n    source;\n    invoke;\n    callback;\n    data;\n    scheduleFn;\n    cancelFn;\n    _zone = null;\n    runCount = 0;\n    _zoneDelegates = null;\n    _state = 'notScheduled';\n    constructor(type, source, callback, options, scheduleFn, cancelFn) {\n      this.type = type;\n      this.source = source;\n      this.data = options;\n      this.scheduleFn = scheduleFn;\n      this.cancelFn = cancelFn;\n      if (!callback) {\n        throw new Error('callback is not defined');\n      }\n      this.callback = callback;\n      const self = this;\n      // TODO: @JiaLiPassion options should have interface\n      if (type === eventTask && options && options.useG) {\n        this.invoke = ZoneTask.invokeTask;\n      } else {\n        this.invoke = function () {\n          return ZoneTask.invokeTask.call(global, self, this, arguments);\n        };\n      }\n    }\n    static invokeTask(task, target, args) {\n      if (!task) {\n        task = this;\n      }\n      _numberOfNestedTaskFrames++;\n      try {\n        task.runCount++;\n        return task.zone.runTask(task, target, args);\n      } finally {\n        if (_numberOfNestedTaskFrames == 1) {\n          drainMicroTaskQueue();\n        }\n        _numberOfNestedTaskFrames--;\n      }\n    }\n    get zone() {\n      return this._zone;\n    }\n    get state() {\n      return this._state;\n    }\n    cancelScheduleRequest() {\n      this._transitionTo(notScheduled, scheduling);\n    }\n    _transitionTo(toState, fromState1, fromState2) {\n      if (this._state === fromState1 || this._state === fromState2) {\n        this._state = toState;\n        if (toState == notScheduled) {\n          this._zoneDelegates = null;\n        }\n      } else {\n        throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? \" or '\" + fromState2 + \"'\" : ''}, was '${this._state}'.`);\n      }\n    }\n    toString() {\n      if (this.data && typeof this.data.handleId !== 'undefined') {\n        return this.data.handleId.toString();\n      } else {\n        return Object.prototype.toString.call(this);\n      }\n    }\n    // add toJSON method to prevent cyclic error when\n    // call JSON.stringify(zoneTask)\n    toJSON() {\n      return {\n        type: this.type,\n        state: this.state,\n        source: this.source,\n        zone: this.zone.name,\n        runCount: this.runCount\n      };\n    }\n  }\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  MICROTASK QUEUE\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  const symbolSetTimeout = __symbol__('setTimeout');\n  const symbolPromise = __symbol__('Promise');\n  const symbolThen = __symbol__('then');\n  let _microTaskQueue = [];\n  let _isDrainingMicrotaskQueue = false;\n  let nativeMicroTaskQueuePromise;\n  function nativeScheduleMicroTask(func) {\n    if (!nativeMicroTaskQueuePromise) {\n      if (global[symbolPromise]) {\n        nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n      }\n    }\n    if (nativeMicroTaskQueuePromise) {\n      let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n      if (!nativeThen) {\n        // native Promise is not patchable, we need to use `then` directly\n        // issue 1078\n        nativeThen = nativeMicroTaskQueuePromise['then'];\n      }\n      nativeThen.call(nativeMicroTaskQueuePromise, func);\n    } else {\n      global[symbolSetTimeout](func, 0);\n    }\n  }\n  function scheduleMicroTask(task) {\n    // if we are not running in any task, and there has not been anything scheduled\n    // we must bootstrap the initial task creation by manually scheduling the drain\n    if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n      // We are not running in Task, so we need to kickstart the microtask queue.\n      nativeScheduleMicroTask(drainMicroTaskQueue);\n    }\n    task && _microTaskQueue.push(task);\n  }\n  function drainMicroTaskQueue() {\n    if (!_isDrainingMicrotaskQueue) {\n      _isDrainingMicrotaskQueue = true;\n      while (_microTaskQueue.length) {\n        const queue = _microTaskQueue;\n        _microTaskQueue = [];\n        for (let i = 0; i < queue.length; i++) {\n          const task = queue[i];\n          try {\n            task.zone.runTask(task, null, null);\n          } catch (error) {\n            _api.onUnhandledError(error);\n          }\n        }\n      }\n      _api.microtaskDrainDone();\n      _isDrainingMicrotaskQueue = false;\n    }\n  }\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  BOOTSTRAP\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  const NO_ZONE = {\n    name: 'NO ZONE'\n  };\n  const notScheduled = 'notScheduled',\n    scheduling = 'scheduling',\n    scheduled = 'scheduled',\n    running = 'running',\n    canceling = 'canceling',\n    unknown = 'unknown';\n  const microTask = 'microTask',\n    macroTask = 'macroTask',\n    eventTask = 'eventTask';\n  const patches = {};\n  const _api = {\n    symbol: __symbol__,\n    currentZoneFrame: () => _currentZoneFrame,\n    onUnhandledError: noop,\n    microtaskDrainDone: noop,\n    scheduleMicroTask: scheduleMicroTask,\n    showUncaughtError: () => !ZoneImpl[__symbol__('ignoreConsoleErrorUncaughtError')],\n    patchEventTarget: () => [],\n    patchOnProperties: noop,\n    patchMethod: () => noop,\n    bindArguments: () => [],\n    patchThen: () => noop,\n    patchMacroTask: () => noop,\n    patchEventPrototype: () => noop,\n    isIEOrEdge: () => false,\n    getGlobalObjects: () => undefined,\n    ObjectDefineProperty: () => noop,\n    ObjectGetOwnPropertyDescriptor: () => undefined,\n    ObjectCreate: () => undefined,\n    ArraySlice: () => [],\n    patchClass: () => noop,\n    wrapWithCurrentZone: () => noop,\n    filterProperties: () => [],\n    attachOriginToPatched: () => noop,\n    _redefineProperty: () => noop,\n    patchCallbacks: () => noop,\n    nativeScheduleMicroTask: nativeScheduleMicroTask\n  };\n  let _currentZoneFrame = {\n    parent: null,\n    zone: new ZoneImpl(null, null)\n  };\n  let _currentTask = null;\n  let _numberOfNestedTaskFrames = 0;\n  function noop() {}\n  performanceMeasure('Zone', 'Zone');\n  return ZoneImpl;\n}\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = __symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n  return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n  return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = __symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = isWindowExists && internalWindow || globalThis;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (typeof args[i] === 'function') {\n      args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n    }\n  }\n  return args;\n}\nfunction isPropertyWritable(propertyDesc) {\n  if (!propertyDesc) {\n    return true;\n  }\n  if (propertyDesc.writable === false) {\n    return false;\n  }\n  return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = !('nw' in _global) && typeof _global.process !== 'undefined' && _global.process.toString() === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' && _global.process.toString() === '[object process]' && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst enableBeforeunloadSymbol = zoneSymbol('enable_beforeunload');\nconst wrapFn = function (event) {\n  // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n  // event will be undefined, so we need to use window.event\n  event = event || _global.event;\n  if (!event) {\n    return;\n  }\n  let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n  }\n  const target = this || event.target || _global;\n  const listener = target[eventNameSymbol];\n  let result;\n  if (isBrowser && target === internalWindow && event.type === 'error') {\n    // window.onerror have different signature\n    // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n    // and onerror callback will prevent default when callback return true\n    const errorEvent = event;\n    result = listener && listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n    if (result === true) {\n      event.preventDefault();\n    }\n  } else {\n    result = listener && listener.apply(this, arguments);\n    if (\n    // https://github.com/angular/angular/issues/47579\n    // https://www.w3.org/TR/2011/WD-html5-20110525/history.html#beforeunloadevent\n    // This is the only specific case we should check for. The spec defines that the\n    // `returnValue` attribute represents the message to show the user. When the event\n    // is created, this attribute must be set to the empty string.\n    event.type === 'beforeunload' &&\n    // To prevent any breaking changes resulting from this change, given that\n    // it was already causing a significant number of failures in G3, we have hidden\n    // that behavior behind a global configuration flag. Consumers can enable this\n    // flag explicitly if they want the `beforeunload` event to be handled as defined\n    // in the specification.\n    _global[enableBeforeunloadSymbol] &&\n    // The IDL event definition is `attribute DOMString returnValue`, so we check whether\n    // `typeof result` is a string.\n    typeof result === 'string') {\n      event.returnValue = result;\n    } else if (result != undefined && !result) {\n      event.preventDefault();\n    }\n  }\n  return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n  let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n  if (!desc && prototype) {\n    // when patch window object, use prototype to check prop exist or not\n    const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n    if (prototypeDesc) {\n      desc = {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  }\n  // if the descriptor not exists or is not configurable\n  // just return\n  if (!desc || !desc.configurable) {\n    return;\n  }\n  const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n  if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n    return;\n  }\n  // A property descriptor cannot have getter/setter and be writable\n  // deleting the writable and value properties avoids this error:\n  //\n  // TypeError: property descriptors must not specify a value or be writable when a\n  // getter or setter has been specified\n  delete desc.writable;\n  delete desc.value;\n  const originalDescGet = desc.get;\n  const originalDescSet = desc.set;\n  // slice(2) cuz 'onclick' -> 'click', etc\n  const eventName = prop.slice(2);\n  let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n  }\n  desc.set = function (newValue) {\n    // In some versions of Windows, the `this` context may be undefined\n    // in on-property callbacks.\n    // To handle this edge case, we check if `this` is falsy and\n    // fallback to `_global` if needed.\n    let target = this;\n    if (!target && obj === _global) {\n      target = _global;\n    }\n    if (!target) {\n      return;\n    }\n    const previousValue = target[eventNameSymbol];\n    if (typeof previousValue === 'function') {\n      target.removeEventListener(eventName, wrapFn);\n    }\n    // https://github.com/angular/zone.js/issues/978\n    // If an inline handler (like `onload`) was defined before zone.js was loaded,\n    // call the original descriptor's setter to clean it up.\n    originalDescSet?.call(target, null);\n    target[eventNameSymbol] = newValue;\n    if (typeof newValue === 'function') {\n      target.addEventListener(eventName, wrapFn, false);\n    }\n  };\n  // The getter would return undefined for unassigned properties but the default value of an\n  // unassigned property is null\n  desc.get = function () {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n    if (!target && obj === _global) {\n      target = _global;\n    }\n    if (!target) {\n      return null;\n    }\n    const listener = target[eventNameSymbol];\n    if (listener) {\n      return listener;\n    } else if (originalDescGet) {\n      // result will be null when use inline event attribute,\n      // such as <button onclick=\"func();\">OK</button>\n      // because the onclick function is internal raw uncompiled handler\n      // the onclick will be evaluated when first time event was triggered or\n      // the property is accessed, https://github.com/angular/zone.js/issues/525\n      // so we should use original native get to retrieve the handler\n      let value = originalDescGet.call(this);\n      if (value) {\n        desc.set.call(this, value);\n        if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n          target.removeAttribute(prop);\n        }\n        return value;\n      }\n    }\n    return null;\n  };\n  ObjectDefineProperty(obj, prop, desc);\n  obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n  if (properties) {\n    for (let i = 0; i < properties.length; i++) {\n      patchProperty(obj, 'on' + properties[i], prototype);\n    }\n  } else {\n    const onProperties = [];\n    for (const prop in obj) {\n      if (prop.slice(0, 2) == 'on') {\n        onProperties.push(prop);\n      }\n    }\n    for (let j = 0; j < onProperties.length; j++) {\n      patchProperty(obj, onProperties[j], prototype);\n    }\n  }\n}\nfunction copySymbolProperties(src, dest) {\n  if (typeof Object.getOwnPropertySymbols !== 'function') {\n    return;\n  }\n  const symbols = Object.getOwnPropertySymbols(src);\n  symbols.forEach(symbol => {\n    const desc = Object.getOwnPropertyDescriptor(src, symbol);\n    Object.defineProperty(dest, symbol, {\n      get: function () {\n        return src[symbol];\n      },\n      set: function (value) {\n        if (desc && (!desc.writable || typeof desc.set !== 'function')) {\n          // if src[symbol] is not writable or not have a setter, just return\n          return;\n        }\n        src[symbol] = value;\n      },\n      enumerable: desc ? desc.enumerable : true,\n      configurable: desc ? desc.configurable : true\n    });\n  });\n}\nlet shouldCopySymbolProperties = false;\nfunction setShouldCopySymbolProperties(flag) {\n  shouldCopySymbolProperties = flag;\n}\nfunction patchMethod(target, name, patchFn) {\n  let proto = target;\n  while (proto && !proto.hasOwnProperty(name)) {\n    proto = ObjectGetPrototypeOf(proto);\n  }\n  if (!proto && target[name]) {\n    // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n    proto = target;\n  }\n  const delegateName = zoneSymbol(name);\n  let delegate = null;\n  if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n    delegate = proto[delegateName] = proto[name];\n    // check whether proto[name] is writable\n    // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n    const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n    if (isPropertyWritable(desc)) {\n      const patchDelegate = patchFn(delegate, delegateName, name);\n      proto[name] = function () {\n        return patchDelegate(this, arguments);\n      };\n      attachOriginToPatched(proto[name], delegate);\n      if (shouldCopySymbolProperties) {\n        copySymbolProperties(delegate, proto[name]);\n      }\n    }\n  }\n  return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n  let setNative = null;\n  function scheduleTask(task) {\n    const data = task.data;\n    data.args[data.cbIdx] = function () {\n      task.invoke.apply(this, arguments);\n    };\n    setNative.apply(data.target, data.args);\n    return task;\n  }\n  setNative = patchMethod(obj, funcName, delegate => function (self, args) {\n    const meta = metaCreator(self, args);\n    if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n      return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(self, args);\n    }\n  });\n}\nfunction patchMicroTask(obj, funcName, metaCreator) {\n  let setNative = null;\n  function scheduleTask(task) {\n    const data = task.data;\n    data.args[data.cbIdx] = function () {\n      task.invoke.apply(this, arguments);\n    };\n    setNative.apply(data.target, data.args);\n    return task;\n  }\n  setNative = patchMethod(obj, funcName, delegate => function (self, args) {\n    const meta = metaCreator(self, args);\n    if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n      return Zone.current.scheduleMicroTask(meta.name, args[meta.cbIdx], meta, scheduleTask);\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(self, args);\n    }\n  });\n}\nfunction attachOriginToPatched(patched, original) {\n  patched[zoneSymbol('OriginalDelegate')] = original;\n}\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\nfunction patchPromise(Zone) {\n  Zone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n    const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    const ObjectDefineProperty = Object.defineProperty;\n    function readableObjectToString(obj) {\n      if (obj && obj.toString === Object.prototype.toString) {\n        const className = obj.constructor && obj.constructor.name;\n        return (className ? className : '') + ': ' + JSON.stringify(obj);\n      }\n      return obj ? obj.toString() : Object.prototype.toString.call(obj);\n    }\n    const __symbol__ = api.symbol;\n    const _uncaughtPromiseErrors = [];\n    const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] !== false;\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    const creationTrace = '__creationTrace__';\n    api.onUnhandledError = e => {\n      if (api.showUncaughtError()) {\n        const rejection = e && e.rejection;\n        if (rejection) {\n          console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n        } else {\n          console.error(e);\n        }\n      }\n    };\n    api.microtaskDrainDone = () => {\n      while (_uncaughtPromiseErrors.length) {\n        const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n        try {\n          uncaughtPromiseError.zone.runGuarded(() => {\n            if (uncaughtPromiseError.throwOriginal) {\n              throw uncaughtPromiseError.rejection;\n            }\n            throw uncaughtPromiseError;\n          });\n        } catch (error) {\n          handleUnhandledRejection(error);\n        }\n      }\n    };\n    const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n    function handleUnhandledRejection(e) {\n      api.onUnhandledError(e);\n      try {\n        const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n        if (typeof handler === 'function') {\n          handler.call(this, e);\n        }\n      } catch (err) {}\n    }\n    function isThenable(value) {\n      return value && typeof value.then === 'function';\n    }\n    function forwardResolution(value) {\n      return value;\n    }\n    function forwardRejection(rejection) {\n      return ZoneAwarePromise.reject(rejection);\n    }\n    const symbolState = __symbol__('state');\n    const symbolValue = __symbol__('value');\n    const symbolFinally = __symbol__('finally');\n    const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n    const symbolParentPromiseState = __symbol__('parentPromiseState');\n    const source = 'Promise.then';\n    const UNRESOLVED = null;\n    const RESOLVED = true;\n    const REJECTED = false;\n    const REJECTED_NO_CATCH = 0;\n    function makeResolver(promise, state) {\n      return v => {\n        try {\n          resolvePromise(promise, state, v);\n        } catch (err) {\n          resolvePromise(promise, false, err);\n        }\n        // Do not return value or you will break the Promise spec.\n      };\n    }\n    const once = function () {\n      let wasCalled = false;\n      return function wrapper(wrappedFunction) {\n        return function () {\n          if (wasCalled) {\n            return;\n          }\n          wasCalled = true;\n          wrappedFunction.apply(null, arguments);\n        };\n      };\n    };\n    const TYPE_ERROR = 'Promise resolved with itself';\n    const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n    // Promise Resolution\n    function resolvePromise(promise, state, value) {\n      const onceWrapper = once();\n      if (promise === value) {\n        throw new TypeError(TYPE_ERROR);\n      }\n      if (promise[symbolState] === UNRESOLVED) {\n        // should only get value.then once based on promise spec.\n        let then = null;\n        try {\n          if (typeof value === 'object' || typeof value === 'function') {\n            then = value && value.then;\n          }\n        } catch (err) {\n          onceWrapper(() => {\n            resolvePromise(promise, false, err);\n          })();\n          return promise;\n        }\n        // if (value instanceof ZoneAwarePromise) {\n        if (state !== REJECTED && value instanceof ZoneAwarePromise && value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) && value[symbolState] !== UNRESOLVED) {\n          clearRejectedNoCatch(value);\n          resolvePromise(promise, value[symbolState], value[symbolValue]);\n        } else if (state !== REJECTED && typeof then === 'function') {\n          try {\n            then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n          } catch (err) {\n            onceWrapper(() => {\n              resolvePromise(promise, false, err);\n            })();\n          }\n        } else {\n          promise[symbolState] = state;\n          const queue = promise[symbolValue];\n          promise[symbolValue] = value;\n          if (promise[symbolFinally] === symbolFinally) {\n            // the promise is generated by Promise.prototype.finally\n            if (state === RESOLVED) {\n              // the state is resolved, should ignore the value\n              // and use parent promise value\n              promise[symbolState] = promise[symbolParentPromiseState];\n              promise[symbolValue] = promise[symbolParentPromiseValue];\n            }\n          }\n          // record task information in value when error occurs, so we can\n          // do some additional work such as render longStackTrace\n          if (state === REJECTED && value instanceof Error) {\n            // check if longStackTraceZone is here\n            const trace = Zone.currentTask && Zone.currentTask.data && Zone.currentTask.data[creationTrace];\n            if (trace) {\n              // only keep the long stack trace into error when in longStackTraceZone\n              ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                configurable: true,\n                enumerable: false,\n                writable: true,\n                value: trace\n              });\n            }\n          }\n          for (let i = 0; i < queue.length;) {\n            scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n          }\n          if (queue.length == 0 && state == REJECTED) {\n            promise[symbolState] = REJECTED_NO_CATCH;\n            let uncaughtPromiseError = value;\n            try {\n              // Here we throws a new Error to print more readable error log\n              // and if the value is not an error, zone.js builds an `Error`\n              // Object here to attach the stack information.\n              throw new Error('Uncaught (in promise): ' + readableObjectToString(value) + (value && value.stack ? '\\n' + value.stack : ''));\n            } catch (err) {\n              uncaughtPromiseError = err;\n            }\n            if (isDisableWrappingUncaughtPromiseRejection) {\n              // If disable wrapping uncaught promise reject\n              // use the value instead of wrapping it.\n              uncaughtPromiseError.throwOriginal = true;\n            }\n            uncaughtPromiseError.rejection = value;\n            uncaughtPromiseError.promise = promise;\n            uncaughtPromiseError.zone = Zone.current;\n            uncaughtPromiseError.task = Zone.currentTask;\n            _uncaughtPromiseErrors.push(uncaughtPromiseError);\n            api.scheduleMicroTask(); // to make sure that it is running\n          }\n        }\n      }\n      // Resolving an already resolved promise is a noop.\n      return promise;\n    }\n    const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n    function clearRejectedNoCatch(promise) {\n      if (promise[symbolState] === REJECTED_NO_CATCH) {\n        // if the promise is rejected no catch status\n        // and queue.length > 0, means there is a error handler\n        // here to handle the rejected promise, we should trigger\n        // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n        // eventHandler\n        try {\n          const handler = Zone[REJECTION_HANDLED_HANDLER];\n          if (handler && typeof handler === 'function') {\n            handler.call(this, {\n              rejection: promise[symbolValue],\n              promise: promise\n            });\n          }\n        } catch (err) {}\n        promise[symbolState] = REJECTED;\n        for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n          if (promise === _uncaughtPromiseErrors[i].promise) {\n            _uncaughtPromiseErrors.splice(i, 1);\n          }\n        }\n      }\n    }\n    function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n      clearRejectedNoCatch(promise);\n      const promiseState = promise[symbolState];\n      const delegate = promiseState ? typeof onFulfilled === 'function' ? onFulfilled : forwardResolution : typeof onRejected === 'function' ? onRejected : forwardRejection;\n      zone.scheduleMicroTask(source, () => {\n        try {\n          const parentPromiseValue = promise[symbolValue];\n          const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n          if (isFinallyPromise) {\n            // if the promise is generated from finally call, keep parent promise's state and value\n            chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n            chainPromise[symbolParentPromiseState] = promiseState;\n          }\n          // should not pass value to finally callback\n          const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ? [] : [parentPromiseValue]);\n          resolvePromise(chainPromise, true, value);\n        } catch (error) {\n          // if error occurs, should always return this error\n          resolvePromise(chainPromise, false, error);\n        }\n      }, chainPromise);\n    }\n    const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n    const noop = function () {};\n    const AggregateError = global.AggregateError;\n    class ZoneAwarePromise {\n      static toString() {\n        return ZONE_AWARE_PROMISE_TO_STRING;\n      }\n      static resolve(value) {\n        if (value instanceof ZoneAwarePromise) {\n          return value;\n        }\n        return resolvePromise(new this(null), RESOLVED, value);\n      }\n      static reject(error) {\n        return resolvePromise(new this(null), REJECTED, error);\n      }\n      static withResolvers() {\n        const result = {};\n        result.promise = new ZoneAwarePromise((res, rej) => {\n          result.resolve = res;\n          result.reject = rej;\n        });\n        return result;\n      }\n      static any(values) {\n        if (!values || typeof values[Symbol.iterator] !== 'function') {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        const promises = [];\n        let count = 0;\n        try {\n          for (let v of values) {\n            count++;\n            promises.push(ZoneAwarePromise.resolve(v));\n          }\n        } catch (err) {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        if (count === 0) {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        let finished = false;\n        const errors = [];\n        return new ZoneAwarePromise((resolve, reject) => {\n          for (let i = 0; i < promises.length; i++) {\n            promises[i].then(v => {\n              if (finished) {\n                return;\n              }\n              finished = true;\n              resolve(v);\n            }, err => {\n              errors.push(err);\n              count--;\n              if (count === 0) {\n                finished = true;\n                reject(new AggregateError(errors, 'All promises were rejected'));\n              }\n            });\n          }\n        });\n      }\n      static race(values) {\n        let resolve;\n        let reject;\n        let promise = new this((res, rej) => {\n          resolve = res;\n          reject = rej;\n        });\n        function onResolve(value) {\n          resolve(value);\n        }\n        function onReject(error) {\n          reject(error);\n        }\n        for (let value of values) {\n          if (!isThenable(value)) {\n            value = this.resolve(value);\n          }\n          value.then(onResolve, onReject);\n        }\n        return promise;\n      }\n      static all(values) {\n        return ZoneAwarePromise.allWithCallback(values);\n      }\n      static allSettled(values) {\n        const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n        return P.allWithCallback(values, {\n          thenCallback: value => ({\n            status: 'fulfilled',\n            value\n          }),\n          errorCallback: err => ({\n            status: 'rejected',\n            reason: err\n          })\n        });\n      }\n      static allWithCallback(values, callback) {\n        let resolve;\n        let reject;\n        let promise = new this((res, rej) => {\n          resolve = res;\n          reject = rej;\n        });\n        // Start at 2 to prevent prematurely resolving if .then is called immediately.\n        let unresolvedCount = 2;\n        let valueIndex = 0;\n        const resolvedValues = [];\n        for (let value of values) {\n          if (!isThenable(value)) {\n            value = this.resolve(value);\n          }\n          const curValueIndex = valueIndex;\n          try {\n            value.then(value => {\n              resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n              unresolvedCount--;\n              if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n              }\n            }, err => {\n              if (!callback) {\n                reject(err);\n              } else {\n                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                unresolvedCount--;\n                if (unresolvedCount === 0) {\n                  resolve(resolvedValues);\n                }\n              }\n            });\n          } catch (thenErr) {\n            reject(thenErr);\n          }\n          unresolvedCount++;\n          valueIndex++;\n        }\n        // Make the unresolvedCount zero-based again.\n        unresolvedCount -= 2;\n        if (unresolvedCount === 0) {\n          resolve(resolvedValues);\n        }\n        return promise;\n      }\n      constructor(executor) {\n        const promise = this;\n        if (!(promise instanceof ZoneAwarePromise)) {\n          throw new Error('Must be an instanceof Promise.');\n        }\n        promise[symbolState] = UNRESOLVED;\n        promise[symbolValue] = []; // queue;\n        try {\n          const onceWrapper = once();\n          executor && executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n        } catch (error) {\n          resolvePromise(promise, false, error);\n        }\n      }\n      get [Symbol.toStringTag]() {\n        return 'Promise';\n      }\n      get [Symbol.species]() {\n        return ZoneAwarePromise;\n      }\n      then(onFulfilled, onRejected) {\n        // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n        // may be an object without a prototype (created through `Object.create(null)`); thus\n        // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n        // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n        // object and copies promise properties into that object (within the `getOrCreateLoad`\n        // function). The zone.js then checks if the resolved value has the `then` method and\n        // invokes it with the `value` context. Otherwise, this will throw an error: `TypeError:\n        // Cannot read properties of undefined (reading 'Symbol(Symbol.species)')`.\n        let C = this.constructor?.[Symbol.species];\n        if (!C || typeof C !== 'function') {\n          C = this.constructor || ZoneAwarePromise;\n        }\n        const chainPromise = new C(noop);\n        const zone = Zone.current;\n        if (this[symbolState] == UNRESOLVED) {\n          this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n        } else {\n          scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n        }\n        return chainPromise;\n      }\n      catch(onRejected) {\n        return this.then(null, onRejected);\n      }\n      finally(onFinally) {\n        // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n        let C = this.constructor?.[Symbol.species];\n        if (!C || typeof C !== 'function') {\n          C = ZoneAwarePromise;\n        }\n        const chainPromise = new C(noop);\n        chainPromise[symbolFinally] = symbolFinally;\n        const zone = Zone.current;\n        if (this[symbolState] == UNRESOLVED) {\n          this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n        } else {\n          scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n        }\n        return chainPromise;\n      }\n    }\n    // Protect against aggressive optimizers dropping seemingly unused properties.\n    // E.g. Closure Compiler in advanced mode.\n    ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n    ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n    ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n    ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n    const NativePromise = global[symbolPromise] = global['Promise'];\n    global['Promise'] = ZoneAwarePromise;\n    const symbolThenPatched = __symbol__('thenPatched');\n    function patchThen(Ctor) {\n      const proto = Ctor.prototype;\n      const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n      if (prop && (prop.writable === false || !prop.configurable)) {\n        // check Ctor.prototype.then propertyDescriptor is writable or not\n        // in meteor env, writable is false, we should ignore such case\n        return;\n      }\n      const originalThen = proto.then;\n      // Keep a reference to the original method.\n      proto[symbolThen] = originalThen;\n      Ctor.prototype.then = function (onResolve, onReject) {\n        const wrapped = new ZoneAwarePromise((resolve, reject) => {\n          originalThen.call(this, resolve, reject);\n        });\n        return wrapped.then(onResolve, onReject);\n      };\n      Ctor[symbolThenPatched] = true;\n    }\n    api.patchThen = patchThen;\n    function zoneify(fn) {\n      return function (self, args) {\n        let resultPromise = fn.apply(self, args);\n        if (resultPromise instanceof ZoneAwarePromise) {\n          return resultPromise;\n        }\n        let ctor = resultPromise.constructor;\n        if (!ctor[symbolThenPatched]) {\n          patchThen(ctor);\n        }\n        return resultPromise;\n      };\n    }\n    if (NativePromise) {\n      patchThen(NativePromise);\n      patchMethod(global, 'fetch', delegate => zoneify(delegate));\n    }\n    // This is not part of public API, but it is useful for tests, so we expose it.\n    Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n    return ZoneAwarePromise;\n  });\n}\nfunction patchToString(Zone) {\n  // override Function.prototype.toString to make zone.js patched function\n  // look like native function\n  Zone.__load_patch('toString', global => {\n    // patch Func.prototype.toString to let them look like native\n    const originalFunctionToString = Function.prototype.toString;\n    const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n    const PROMISE_SYMBOL = zoneSymbol('Promise');\n    const ERROR_SYMBOL = zoneSymbol('Error');\n    const newFunctionToString = function toString() {\n      if (typeof this === 'function') {\n        const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n        if (originalDelegate) {\n          if (typeof originalDelegate === 'function') {\n            return originalFunctionToString.call(originalDelegate);\n          } else {\n            return Object.prototype.toString.call(originalDelegate);\n          }\n        }\n        if (this === Promise) {\n          const nativePromise = global[PROMISE_SYMBOL];\n          if (nativePromise) {\n            return originalFunctionToString.call(nativePromise);\n          }\n        }\n        if (this === Error) {\n          const nativeError = global[ERROR_SYMBOL];\n          if (nativeError) {\n            return originalFunctionToString.call(nativeError);\n          }\n        }\n      }\n      return originalFunctionToString.call(this);\n    };\n    newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n    Function.prototype.toString = newFunctionToString;\n    // patch Object.prototype.toString to let them look like native\n    const originalObjectToString = Object.prototype.toString;\n    const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n    Object.prototype.toString = function () {\n      if (typeof Promise === 'function' && this instanceof Promise) {\n        return PROMISE_OBJECT_TO_STRING;\n      }\n      return originalObjectToString.call(this);\n    };\n  });\n}\nfunction loadZone() {\n  // if global['Zone'] already exists (maybe zone.js was already loaded or\n  // some other lib also registered a global object named Zone), we may need\n  // to throw an error, but sometimes user may not want this error.\n  // For example,\n  // we have two web pages, page1 includes zone.js, page2 doesn't.\n  // and the 1st time user load page1 and page2, everything work fine,\n  // but when user load page2 again, error occurs because global['Zone'] already exists.\n  // so we add a flag to let user choose whether to throw this error or not.\n  // By default, if existing Zone is from zone.js, we will not throw the error.\n  const global = globalThis;\n  const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n  if (global['Zone'] && (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function')) {\n    throw new Error('Zone already loaded.');\n  }\n  // Initialize global `Zone` constant.\n  global['Zone'] ??= initZone();\n  return global['Zone'];\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n  useG: true\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n  const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n  const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n  const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n  const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n  zoneSymbolEventNames[eventName] = {};\n  zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n  zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n  const ADD_EVENT_LISTENER = patchOptions && patchOptions.add || ADD_EVENT_LISTENER_STR;\n  const REMOVE_EVENT_LISTENER = patchOptions && patchOptions.rm || REMOVE_EVENT_LISTENER_STR;\n  const LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.listeners || 'eventListeners';\n  const REMOVE_ALL_LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.rmAll || 'removeAllListeners';\n  const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n  const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n  const PREPEND_EVENT_LISTENER = 'prependListener';\n  const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n  const invokeTask = function (task, target, event) {\n    // for better performance, check isRemoved which is set\n    // by removeEventListener\n    if (task.isRemoved) {\n      return;\n    }\n    const delegate = task.callback;\n    if (typeof delegate === 'object' && delegate.handleEvent) {\n      // create the bind version of handleEvent when invoke\n      task.callback = event => delegate.handleEvent(event);\n      task.originalDelegate = delegate;\n    }\n    // invoke static task.invoke\n    // need to try/catch error here, otherwise, the error in one event listener\n    // will break the executions of the other event listeners. Also error will\n    // not remove the event listener when `once` options is true.\n    let error;\n    try {\n      task.invoke(task, target, [event]);\n    } catch (err) {\n      error = err;\n    }\n    const options = task.options;\n    if (options && typeof options === 'object' && options.once) {\n      // if options.once is true, after invoke once remove listener here\n      // only browser need to do this, nodejs eventEmitter will cal removeListener\n      // inside EventEmitter.once\n      const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n      target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n    }\n    return error;\n  };\n  function globalCallback(context, event, isCapture) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n      return;\n    }\n    // event.target is needed for Samsung TV and SourceBuffer\n    // || global is needed https://github.com/angular/zone.js/issues/190\n    const target = context || event.target || _global;\n    const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n    if (tasks) {\n      const errors = [];\n      // invoke all tasks which attached to current target with given event.type and capture = false\n      // for performance concern, if task.length === 1, just invoke\n      if (tasks.length === 1) {\n        const err = invokeTask(tasks[0], target, event);\n        err && errors.push(err);\n      } else {\n        // https://github.com/angular/zone.js/issues/836\n        // copy the tasks array before invoke, to avoid\n        // the callback will remove itself or other listener\n        const copyTasks = tasks.slice();\n        for (let i = 0; i < copyTasks.length; i++) {\n          if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n            break;\n          }\n          const err = invokeTask(copyTasks[i], target, event);\n          err && errors.push(err);\n        }\n      }\n      // Since there is only one error, we don't need to schedule microTask\n      // to throw the error.\n      if (errors.length === 1) {\n        throw errors[0];\n      } else {\n        for (let i = 0; i < errors.length; i++) {\n          const err = errors[i];\n          api.nativeScheduleMicroTask(() => {\n            throw err;\n          });\n        }\n      }\n    }\n  }\n  // global shared zoneAwareCallback to handle all event callback with capture = false\n  const globalZoneAwareCallback = function (event) {\n    return globalCallback(this, event, false);\n  };\n  // global shared zoneAwareCallback to handle all event callback with capture = true\n  const globalZoneAwareCaptureCallback = function (event) {\n    return globalCallback(this, event, true);\n  };\n  function patchEventTargetMethods(obj, patchOptions) {\n    if (!obj) {\n      return false;\n    }\n    let useGlobalCallback = true;\n    if (patchOptions && patchOptions.useG !== undefined) {\n      useGlobalCallback = patchOptions.useG;\n    }\n    const validateHandler = patchOptions && patchOptions.vh;\n    let checkDuplicate = true;\n    if (patchOptions && patchOptions.chkDup !== undefined) {\n      checkDuplicate = patchOptions.chkDup;\n    }\n    let returnTarget = false;\n    if (patchOptions && patchOptions.rt !== undefined) {\n      returnTarget = patchOptions.rt;\n    }\n    let proto = obj;\n    while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n      proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && obj[ADD_EVENT_LISTENER]) {\n      // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n      proto = obj;\n    }\n    if (!proto) {\n      return false;\n    }\n    if (proto[zoneSymbolAddEventListener]) {\n      return false;\n    }\n    const eventNameToString = patchOptions && patchOptions.eventNameToString;\n    // We use a shared global `taskData` to pass data for `scheduleEventTask`,\n    // eliminating the need to create a new object solely for passing data.\n    // WARNING: This object has a static lifetime, meaning it is not created\n    // each time `addEventListener` is called. It is instantiated only once\n    // and captured by reference inside the `addEventListener` and\n    // `removeEventListener` functions. Do not add any new properties to this\n    // object, as doing so would necessitate maintaining the information\n    // between `addEventListener` calls.\n    const taskData = {};\n    const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n    const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] = proto[REMOVE_EVENT_LISTENER];\n    const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] = proto[LISTENERS_EVENT_LISTENER];\n    const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] = proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n    let nativePrependEventListener;\n    if (patchOptions && patchOptions.prepend) {\n      nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] = proto[patchOptions.prepend];\n    }\n    /**\n     * This util function will build an option object with passive option\n     * to handle all possible input from the user.\n     */\n    function buildEventListenerOptions(options, passive) {\n      if (!passive) {\n        return options;\n      }\n      if (typeof options === 'boolean') {\n        return {\n          capture: options,\n          passive: true\n        };\n      }\n      if (!options) {\n        return {\n          passive: true\n        };\n      }\n      if (typeof options === 'object' && options.passive !== false) {\n        return {\n          ...options,\n          passive: true\n        };\n      }\n      return options;\n    }\n    const customScheduleGlobal = function (task) {\n      // if there is already a task for the eventName + capture,\n      // just return, because we use the shared globalZoneAwareCallback here.\n      if (taskData.isExisting) {\n        return;\n      }\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n    };\n    /**\n     * In the context of events and listeners, this function will be\n     * called at the end by `cancelTask`, which, in turn, calls `task.cancelFn`.\n     * Cancelling a task is primarily used to remove event listeners from\n     * the task target.\n     */\n    const customCancelGlobal = function (task) {\n      // if task is not marked as isRemoved, this call is directly\n      // from Zone.prototype.cancelTask, we should remove the task\n      // from tasksList of target first\n      if (!task.isRemoved) {\n        const symbolEventNames = zoneSymbolEventNames[task.eventName];\n        let symbolEventName;\n        if (symbolEventNames) {\n          symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n        }\n        const existingTasks = symbolEventName && task.target[symbolEventName];\n        if (existingTasks) {\n          for (let i = 0; i < existingTasks.length; i++) {\n            const existingTask = existingTasks[i];\n            if (existingTask === task) {\n              existingTasks.splice(i, 1);\n              // set isRemoved to data for faster invokeTask check\n              task.isRemoved = true;\n              if (task.removeAbortListener) {\n                task.removeAbortListener();\n                task.removeAbortListener = null;\n              }\n              if (existingTasks.length === 0) {\n                // all tasks for the eventName + capture have gone,\n                // remove globalZoneAwareCallback and remove the task cache from target\n                task.allRemoved = true;\n                task.target[symbolEventName] = null;\n              }\n              break;\n            }\n          }\n        }\n      }\n      // if all tasks for the eventName + capture have gone,\n      // we will really remove the global event callback,\n      // if not, return\n      if (!task.allRemoved) {\n        return;\n      }\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n    };\n    const customScheduleNonGlobal = function (task) {\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n    const customSchedulePrepend = function (task) {\n      return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n    const customCancelNonGlobal = function (task) {\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n    };\n    const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n    const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n    const compareTaskCallbackVsDelegate = function (task, delegate) {\n      const typeOfDelegate = typeof delegate;\n      return typeOfDelegate === 'function' && task.callback === delegate || typeOfDelegate === 'object' && task.originalDelegate === delegate;\n    };\n    const compare = patchOptions?.diff || compareTaskCallbackVsDelegate;\n    const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n    const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n    function copyEventListenerOptions(options) {\n      if (typeof options === 'object' && options !== null) {\n        // We need to destructure the target `options` object since it may\n        // be frozen or sealed (possibly provided implicitly by a third-party\n        // library), or its properties may be readonly.\n        const newOptions = {\n          ...options\n        };\n        // The `signal` option was recently introduced, which caused regressions in\n        // third-party scenarios where `AbortController` was directly provided to\n        // `addEventListener` as options. For instance, in cases like\n        // `document.addEventListener('keydown', callback, abortControllerInstance)`,\n        // which is valid because `AbortController` includes a `signal` getter, spreading\n        // `{...options}` wouldn't copy the `signal`. Additionally, using `Object.create`\n        // isn't feasible since `AbortController` is a built-in object type, and attempting\n        // to create a new object directly with it as the prototype might result in\n        // unexpected behavior.\n        if (options.signal) {\n          newOptions.signal = options.signal;\n        }\n        return newOptions;\n      }\n      return options;\n    }\n    const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n      return function () {\n        const target = this || _global;\n        let eventName = arguments[0];\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n        let delegate = arguments[1];\n        if (!delegate) {\n          return nativeListener.apply(this, arguments);\n        }\n        if (isNode && eventName === 'uncaughtException') {\n          // don't patch uncaughtException of nodejs to prevent endless loop\n          return nativeListener.apply(this, arguments);\n        }\n        // To improve `addEventListener` performance, we will create the callback\n        // for the task later when the task is invoked.\n        let isEventListenerObject = false;\n        if (typeof delegate !== 'function') {\n          // This checks whether the provided listener argument is an object with\n          // a `handleEvent` method (since we can call `addEventListener` with a\n          // function `event => ...` or with an object `{ handleEvent: event => ... }`).\n          if (!delegate.handleEvent) {\n            return nativeListener.apply(this, arguments);\n          }\n          isEventListenerObject = true;\n        }\n        if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n          return;\n        }\n        const passive = !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n        const options = copyEventListenerOptions(buildEventListenerOptions(arguments[2], passive));\n        const signal = options?.signal;\n        if (signal?.aborted) {\n          // the signal is an aborted one, just return without attaching the event listener.\n          return;\n        }\n        if (unpatchedEvents) {\n          // check unpatched list\n          for (let i = 0; i < unpatchedEvents.length; i++) {\n            if (eventName === unpatchedEvents[i]) {\n              if (passive) {\n                return nativeListener.call(target, eventName, delegate, options);\n              } else {\n                return nativeListener.apply(this, arguments);\n              }\n            }\n          }\n        }\n        const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n        const once = options && typeof options === 'object' ? options.once : false;\n        const zone = Zone.current;\n        let symbolEventNames = zoneSymbolEventNames[eventName];\n        if (!symbolEventNames) {\n          prepareEventNames(eventName, eventNameToString);\n          symbolEventNames = zoneSymbolEventNames[eventName];\n        }\n        const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n        let existingTasks = target[symbolEventName];\n        let isExisting = false;\n        if (existingTasks) {\n          // already have task registered\n          isExisting = true;\n          if (checkDuplicate) {\n            for (let i = 0; i < existingTasks.length; i++) {\n              if (compare(existingTasks[i], delegate)) {\n                // same callback, same capture, same event name, just return\n                return;\n              }\n            }\n          }\n        } else {\n          existingTasks = target[symbolEventName] = [];\n        }\n        let source;\n        const constructorName = target.constructor['name'];\n        const targetSource = globalSources[constructorName];\n        if (targetSource) {\n          source = targetSource[eventName];\n        }\n        if (!source) {\n          source = constructorName + addSource + (eventNameToString ? eventNameToString(eventName) : eventName);\n        }\n        // In the code below, `options` should no longer be reassigned; instead, it\n        // should only be mutated. This is because we pass that object to the native\n        // `addEventListener`.\n        // It's generally recommended to use the same object reference for options.\n        // This ensures consistency and avoids potential issues.\n        taskData.options = options;\n        if (once) {\n          // When using `addEventListener` with the `once` option, we don't pass\n          // the `once` option directly to the native `addEventListener` method.\n          // Instead, we keep the `once` setting and handle it ourselves.\n          taskData.options.once = false;\n        }\n        taskData.target = target;\n        taskData.capture = capture;\n        taskData.eventName = eventName;\n        taskData.isExisting = isExisting;\n        const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n        // keep taskData into data to allow onScheduleEventTask to access the task information\n        if (data) {\n          data.taskData = taskData;\n        }\n        if (signal) {\n          // When using `addEventListener` with the `signal` option, we don't pass\n          // the `signal` option directly to the native `addEventListener` method.\n          // Instead, we keep the `signal` setting and handle it ourselves.\n          taskData.options.signal = undefined;\n        }\n        // The `scheduleEventTask` function will ultimately call `customScheduleGlobal`,\n        // which in turn calls the native `addEventListener`. This is why `taskData.options`\n        // is updated before scheduling the task, as `customScheduleGlobal` uses\n        // `taskData.options` to pass it to the native `addEventListener`.\n        const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n        if (signal) {\n          // after task is scheduled, we need to store the signal back to task.options\n          taskData.options.signal = signal;\n          // Wrapping `task` in a weak reference would not prevent memory leaks. Weak references are\n          // primarily used for preventing strong references cycles. `onAbort` is always reachable\n          // as it's an event listener, so its closure retains a strong reference to the `task`.\n          const onAbort = () => task.zone.cancelTask(task);\n          nativeListener.call(signal, 'abort', onAbort, {\n            once: true\n          });\n          // We need to remove the `abort` listener when the event listener is going to be removed,\n          // as it creates a closure that captures `task`. This closure retains a reference to the\n          // `task` object even after it goes out of scope, preventing `task` from being garbage\n          // collected.\n          task.removeAbortListener = () => signal.removeEventListener('abort', onAbort);\n        }\n        // should clear taskData.target to avoid memory leak\n        // issue, https://github.com/angular/angular/issues/20442\n        taskData.target = null;\n        // need to clear up taskData because it is a global object\n        if (data) {\n          data.taskData = null;\n        }\n        // have to save those information to task in case\n        // application may call task.zone.cancelTask() directly\n        if (once) {\n          taskData.options.once = true;\n        }\n        if (typeof task.options !== 'boolean') {\n          // We should save the options on the task (if it's an object) because\n          // we'll be using `task.options` later when removing the event listener\n          // and passing it back to `removeEventListener`.\n          task.options = options;\n        }\n        task.target = target;\n        task.capture = capture;\n        task.eventName = eventName;\n        if (isEventListenerObject) {\n          // save original delegate for compare to check duplicate\n          task.originalDelegate = delegate;\n        }\n        if (!prepend) {\n          existingTasks.push(task);\n        } else {\n          existingTasks.unshift(task);\n        }\n        if (returnTarget) {\n          return target;\n        }\n      };\n    };\n    proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n    if (nativePrependEventListener) {\n      proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n    }\n    proto[REMOVE_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n      const options = arguments[2];\n      const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n      const delegate = arguments[1];\n      if (!delegate) {\n        return nativeRemoveEventListener.apply(this, arguments);\n      }\n      if (validateHandler && !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n        return;\n      }\n      const symbolEventNames = zoneSymbolEventNames[eventName];\n      let symbolEventName;\n      if (symbolEventNames) {\n        symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n      }\n      const existingTasks = symbolEventName && target[symbolEventName];\n      // `existingTasks` may not exist if the `addEventListener` was called before\n      // it was patched by zone.js. Please refer to the attached issue for\n      // clarification, particularly after the `if` condition, before calling\n      // the native `removeEventListener`.\n      if (existingTasks) {\n        for (let i = 0; i < existingTasks.length; i++) {\n          const existingTask = existingTasks[i];\n          if (compare(existingTask, delegate)) {\n            existingTasks.splice(i, 1);\n            // set isRemoved to data for faster invokeTask check\n            existingTask.isRemoved = true;\n            if (existingTasks.length === 0) {\n              // all tasks for the eventName + capture have gone,\n              // remove globalZoneAwareCallback and remove the task cache from target\n              existingTask.allRemoved = true;\n              target[symbolEventName] = null;\n              // in the target, we have an event listener which is added by on_property\n              // such as target.onclick = function() {}, so we need to clear this internal\n              // property too if all delegates with capture=false were removed\n              // https:// github.com/angular/angular/issues/31643\n              // https://github.com/angular/angular/issues/54581\n              if (!capture && typeof eventName === 'string') {\n                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                target[onPropertySymbol] = null;\n              }\n            }\n            // In all other conditions, when `addEventListener` is called after being\n            // patched by zone.js, we would always find an event task on the `EventTarget`.\n            // This will trigger `cancelFn` on the `existingTask`, leading to `customCancelGlobal`,\n            // which ultimately removes an event listener and cleans up the abort listener\n            // (if an `AbortSignal` was provided when scheduling a task).\n            existingTask.zone.cancelTask(existingTask);\n            if (returnTarget) {\n              return target;\n            }\n            return;\n          }\n        }\n      }\n      // https://github.com/angular/zone.js/issues/930\n      // We may encounter a situation where the `addEventListener` was\n      // called on the event target before zone.js is loaded, resulting\n      // in no task being stored on the event target due to its invocation\n      // of the native implementation. In this scenario, we simply need to\n      // invoke the native `removeEventListener`.\n      return nativeRemoveEventListener.apply(this, arguments);\n    };\n    proto[LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n      const listeners = [];\n      const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n      for (let i = 0; i < tasks.length; i++) {\n        const task = tasks[i];\n        let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n        listeners.push(delegate);\n      }\n      return listeners;\n    };\n    proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (!eventName) {\n        const keys = Object.keys(target);\n        for (let i = 0; i < keys.length; i++) {\n          const prop = keys[i];\n          const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n          let evtName = match && match[1];\n          // in nodejs EventEmitter, removeListener event is\n          // used for monitoring the removeListener call,\n          // so just keep removeListener eventListener until\n          // all other eventListeners are removed\n          if (evtName && evtName !== 'removeListener') {\n            this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n          }\n        }\n        // remove removeListener listener finally\n        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n      } else {\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n        const symbolEventNames = zoneSymbolEventNames[eventName];\n        if (symbolEventNames) {\n          const symbolEventName = symbolEventNames[FALSE_STR];\n          const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n          const tasks = target[symbolEventName];\n          const captureTasks = target[symbolCaptureEventName];\n          if (tasks) {\n            const removeTasks = tasks.slice();\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n          if (captureTasks) {\n            const removeTasks = captureTasks.slice();\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n        }\n      }\n      if (returnTarget) {\n        return this;\n      }\n    };\n    // for native toString patch\n    attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n    attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n    if (nativeRemoveAllListeners) {\n      attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n    }\n    if (nativeListeners) {\n      attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n    }\n    return true;\n  }\n  let results = [];\n  for (let i = 0; i < apis.length; i++) {\n    results[i] = patchEventTargetMethods(apis[i], patchOptions);\n  }\n  return results;\n}\nfunction findEventTasks(target, eventName) {\n  if (!eventName) {\n    const foundTasks = [];\n    for (let prop in target) {\n      const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n      let evtName = match && match[1];\n      if (evtName && (!eventName || evtName === eventName)) {\n        const tasks = target[prop];\n        if (tasks) {\n          for (let i = 0; i < tasks.length; i++) {\n            foundTasks.push(tasks[i]);\n          }\n        }\n      }\n    }\n    return foundTasks;\n  }\n  let symbolEventName = zoneSymbolEventNames[eventName];\n  if (!symbolEventName) {\n    prepareEventNames(eventName);\n    symbolEventName = zoneSymbolEventNames[eventName];\n  }\n  const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n  const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n  if (!captureFalseTasks) {\n    return captureTrueTasks ? captureTrueTasks.slice() : [];\n  } else {\n    return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) : captureFalseTasks.slice();\n  }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n  api.patchMethod(global, 'queueMicrotask', delegate => {\n    return function (self, args) {\n      Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n    };\n  });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n  let setNative = null;\n  let clearNative = null;\n  setName += nameSuffix;\n  cancelName += nameSuffix;\n  const tasksByHandleId = {};\n  function scheduleTask(task) {\n    const data = task.data;\n    data.args[0] = function () {\n      return task.invoke.apply(this, arguments);\n    };\n    const handleOrId = setNative.apply(window, data.args);\n    // Whlist on Node.js when get can the ID by using `[Symbol.toPrimitive]()` we do\n    // to this so that we do not cause potentally leaks when using `setTimeout`\n    // since this can be periodic when using `.refresh`.\n    if (isNumber(handleOrId)) {\n      data.handleId = handleOrId;\n    } else {\n      data.handle = handleOrId;\n      // On Node.js a timeout and interval can be restarted over and over again by using the `.refresh` method.\n      data.isRefreshable = isFunction(handleOrId.refresh);\n    }\n    return task;\n  }\n  function clearTask(task) {\n    const {\n      handle,\n      handleId\n    } = task.data;\n    return clearNative.call(window, handle ?? handleId);\n  }\n  setNative = patchMethod(window, setName, delegate => function (self, args) {\n    if (isFunction(args[0])) {\n      const options = {\n        isRefreshable: false,\n        isPeriodic: nameSuffix === 'Interval',\n        delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n        args: args\n      };\n      const callback = args[0];\n      args[0] = function timer() {\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          // issue-934, task will be cancelled\n          // even it is a periodic task such as\n          // setInterval\n          // https://github.com/angular/angular/issues/40387\n          // Cleanup tasksByHandleId should be handled before scheduleTask\n          // Since some zoneSpec may intercept and doesn't trigger\n          // scheduleFn(scheduleTask) provided here.\n          const {\n            handle,\n            handleId,\n            isPeriodic,\n            isRefreshable\n          } = options;\n          if (!isPeriodic && !isRefreshable) {\n            if (handleId) {\n              // in non-nodejs env, we remove timerId\n              // from local cache\n              delete tasksByHandleId[handleId];\n            } else if (handle) {\n              // Node returns complex objects as handleIds\n              // we remove task reference from timer object\n              handle[taskSymbol] = null;\n            }\n          }\n        }\n      };\n      const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n      if (!task) {\n        return task;\n      }\n      // Node.js must additionally support the ref and unref functions.\n      const {\n        handleId,\n        handle,\n        isRefreshable,\n        isPeriodic\n      } = task.data;\n      if (handleId) {\n        // for non nodejs env, we save handleId: task\n        // mapping in local cache for clearTimeout\n        tasksByHandleId[handleId] = task;\n      } else if (handle) {\n        // for nodejs env, we save task\n        // reference in timerId Object for clearTimeout\n        handle[taskSymbol] = task;\n        if (isRefreshable && !isPeriodic) {\n          const originalRefresh = handle.refresh;\n          handle.refresh = function () {\n            const {\n              zone,\n              state\n            } = task;\n            if (state === 'notScheduled') {\n              task._state = 'scheduled';\n              zone._updateTaskCount(task, 1);\n            } else if (state === 'running') {\n              task._state = 'scheduling';\n            }\n            return originalRefresh.call(this);\n          };\n        }\n      }\n      return handle ?? handleId ?? task;\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(window, args);\n    }\n  });\n  clearNative = patchMethod(window, cancelName, delegate => function (self, args) {\n    const id = args[0];\n    let task;\n    if (isNumber(id)) {\n      // non nodejs env.\n      task = tasksByHandleId[id];\n      delete tasksByHandleId[id];\n    } else {\n      // nodejs env ?? other environments.\n      task = id?.[taskSymbol];\n      if (task) {\n        id[taskSymbol] = null;\n      } else {\n        task = id;\n      }\n    }\n    if (task?.type) {\n      if (task.cancelFn) {\n        // Do not cancel already canceled functions\n        task.zone.cancelTask(task);\n      }\n    } else {\n      // cause an error by calling it directly.\n      delegate.apply(window, args);\n    }\n  });\n}\nfunction patchEvents(Zone) {\n  Zone.__load_patch('EventEmitter', (global, Zone, api) => {\n    // For EventEmitter\n    const EE_ADD_LISTENER = 'addListener';\n    const EE_PREPEND_LISTENER = 'prependListener';\n    const EE_REMOVE_LISTENER = 'removeListener';\n    const EE_REMOVE_ALL_LISTENER = 'removeAllListeners';\n    const EE_LISTENERS = 'listeners';\n    const EE_ON = 'on';\n    const EE_OFF = 'off';\n    const compareTaskCallbackVsDelegate = function (task, delegate) {\n      // same callback, same capture, same event name, just return\n      return task.callback === delegate || task.callback.listener === delegate;\n    };\n    const eventNameToString = function (eventName) {\n      if (typeof eventName === 'string') {\n        return eventName;\n      }\n      if (!eventName) {\n        return '';\n      }\n      return eventName.toString().replace('(', '_').replace(')', '_');\n    };\n    function patchEventEmitterMethods(obj) {\n      const result = patchEventTarget(global, api, [obj], {\n        useG: false,\n        add: EE_ADD_LISTENER,\n        rm: EE_REMOVE_LISTENER,\n        prepend: EE_PREPEND_LISTENER,\n        rmAll: EE_REMOVE_ALL_LISTENER,\n        listeners: EE_LISTENERS,\n        chkDup: false,\n        rt: true,\n        diff: compareTaskCallbackVsDelegate,\n        eventNameToString: eventNameToString\n      });\n      if (result && result[0]) {\n        obj[EE_ON] = obj[EE_ADD_LISTENER];\n        obj[EE_OFF] = obj[EE_REMOVE_LISTENER];\n      }\n    }\n    // EventEmitter\n    let events;\n    try {\n      events = require('events');\n    } catch (err) {}\n    if (events && events.EventEmitter) {\n      patchEventEmitterMethods(events.EventEmitter.prototype);\n    }\n  });\n}\nfunction patchFs(Zone) {\n  Zone.__load_patch('fs', (global, Zone, api) => {\n    let fs;\n    try {\n      fs = require('fs');\n    } catch (err) {}\n    if (!fs) return;\n    // watch, watchFile, unwatchFile has been patched\n    // because EventEmitter has been patched\n    const TO_PATCH_MACROTASK_METHODS = ['access', 'appendFile', 'chmod', 'chown', 'close', 'exists', 'fchmod', 'fchown', 'fdatasync', 'fstat', 'fsync', 'ftruncate', 'futimes', 'lchmod', 'lchown', 'lutimes', 'link', 'lstat', 'mkdir', 'mkdtemp', 'open', 'opendir', 'read', 'readdir', 'readFile', 'readlink', 'realpath', 'rename', 'rmdir', 'stat', 'symlink', 'truncate', 'unlink', 'utimes', 'write', 'writeFile', 'writev'];\n    TO_PATCH_MACROTASK_METHODS.filter(name => !!fs[name] && typeof fs[name] === 'function').forEach(name => {\n      patchMacroTask(fs, name, (self, args) => {\n        return {\n          name: 'fs.' + name,\n          args: args,\n          cbIdx: args.length > 0 ? args.length - 1 : -1,\n          target: self\n        };\n      });\n    });\n    const realpathOriginalDelegate = fs.realpath?.[api.symbol('OriginalDelegate')];\n    // This is the only specific method that should be additionally patched because the previous\n    // `patchMacroTask` has overridden the `realpath` function and its `native` property.\n    if (realpathOriginalDelegate?.native) {\n      fs.realpath.native = realpathOriginalDelegate.native;\n      patchMacroTask(fs.realpath, 'native', (self, args) => ({\n        args,\n        target: self,\n        cbIdx: args.length > 0 ? args.length - 1 : -1,\n        name: 'fs.realpath.native'\n      }));\n    }\n  });\n}\nfunction patchNodeUtil(Zone) {\n  Zone.__load_patch('node_util', (global, Zone, api) => {\n    api.patchOnProperties = patchOnProperties;\n    api.patchMethod = patchMethod;\n    api.bindArguments = bindArguments;\n    api.patchMacroTask = patchMacroTask;\n    setShouldCopySymbolProperties(true);\n  });\n}\nconst set = 'set';\nconst clear = 'clear';\nfunction patchNode(Zone) {\n  patchNodeUtil(Zone);\n  patchEvents(Zone);\n  patchFs(Zone);\n  Zone.__load_patch('node_timers', (global, Zone) => {\n    // Timers\n    let globalUseTimeoutFromTimer = false;\n    try {\n      const timers = require('timers');\n      let globalEqualTimersTimeout = global.setTimeout === timers.setTimeout;\n      if (!globalEqualTimersTimeout && !isMix) {\n        // 1. if isMix, then we are in mix environment such as Electron\n        // we should only patch timers.setTimeout because global.setTimeout\n        // have been patched\n        // 2. if global.setTimeout not equal timers.setTimeout, check\n        // whether global.setTimeout use timers.setTimeout or not\n        const originSetTimeout = timers.setTimeout;\n        timers.setTimeout = function () {\n          globalUseTimeoutFromTimer = true;\n          return originSetTimeout.apply(this, arguments);\n        };\n        const detectTimeout = global.setTimeout(() => {}, 100);\n        clearTimeout(detectTimeout);\n        timers.setTimeout = originSetTimeout;\n      }\n      patchTimer(timers, set, clear, 'Timeout');\n      patchTimer(timers, set, clear, 'Interval');\n      patchTimer(timers, set, clear, 'Immediate');\n    } catch (error) {\n      // timers module not exists, for example, when we using nativeScript\n      // timers is not available\n    }\n    if (isMix) {\n      // if we are in mix environment, such as Electron,\n      // the global.setTimeout has already been patched,\n      // so we just patch timers.setTimeout\n      return;\n    }\n    if (!globalUseTimeoutFromTimer) {\n      // 1. global setTimeout equals timers setTimeout\n      // 2. or global don't use timers setTimeout(maybe some other library patch setTimeout)\n      // 3. or load timers module error happens, we should patch global setTimeout\n      patchTimer(global, set, clear, 'Timeout');\n      patchTimer(global, set, clear, 'Interval');\n      patchTimer(global, set, clear, 'Immediate');\n    } else {\n      // global use timers setTimeout, but not equals\n      // this happens when use nodejs v0.10.x, global setTimeout will\n      // use a lazy load version of timers setTimeout\n      // we should not double patch timer's setTimeout\n      // so we only store the __symbol__ for consistency\n      global[Zone.__symbol__('setTimeout')] = global.setTimeout;\n      global[Zone.__symbol__('setInterval')] = global.setInterval;\n      global[Zone.__symbol__('setImmediate')] = global.setImmediate;\n    }\n  });\n  // patch process related methods\n  Zone.__load_patch('nextTick', () => {\n    // patch nextTick as microTask\n    patchMicroTask(process, 'nextTick', (self, args) => {\n      return {\n        name: 'process.nextTick',\n        args: args,\n        cbIdx: args.length > 0 && typeof args[0] === 'function' ? 0 : -1,\n        target: process\n      };\n    });\n  });\n  Zone.__load_patch('handleUnhandledPromiseRejection', (global, Zone, api) => {\n    Zone[api.symbol('unhandledPromiseRejectionHandler')] = findProcessPromiseRejectionHandler('unhandledRejection');\n    Zone[api.symbol('rejectionHandledHandler')] = findProcessPromiseRejectionHandler('rejectionHandled');\n    // handle unhandled promise rejection\n    function findProcessPromiseRejectionHandler(evtName) {\n      return function (e) {\n        const eventTasks = findEventTasks(process, evtName);\n        eventTasks.forEach(eventTask => {\n          // process has added unhandledrejection event listener\n          // trigger the event listener\n          if (evtName === 'unhandledRejection') {\n            eventTask.invoke(e.rejection, e.promise);\n          } else if (evtName === 'rejectionHandled') {\n            eventTask.invoke(e.promise);\n          }\n        });\n      };\n    }\n  });\n  // Crypto\n  Zone.__load_patch('crypto', () => {\n    let crypto;\n    try {\n      crypto = require('crypto');\n    } catch (err) {}\n    // use the generic patchMacroTask to patch crypto\n    if (crypto) {\n      const methodNames = ['randomBytes', 'pbkdf2'];\n      methodNames.forEach(name => {\n        patchMacroTask(crypto, name, (self, args) => {\n          return {\n            name: 'crypto.' + name,\n            args: args,\n            cbIdx: args.length > 0 && typeof args[args.length - 1] === 'function' ? args.length - 1 : -1,\n            target: crypto\n          };\n        });\n      });\n    }\n  });\n  Zone.__load_patch('console', (global, Zone) => {\n    const consoleMethods = ['dir', 'log', 'info', 'error', 'warn', 'assert', 'debug', 'timeEnd', 'trace'];\n    consoleMethods.forEach(m => {\n      const originalMethod = console[Zone.__symbol__(m)] = console[m];\n      if (originalMethod) {\n        console[m] = function () {\n          const args = ArraySlice.call(arguments);\n          if (Zone.current === Zone.root) {\n            return originalMethod.apply(this, args);\n          } else {\n            return Zone.root.run(originalMethod, this, args);\n          }\n        };\n      }\n    });\n  });\n  Zone.__load_patch('queueMicrotask', (global, Zone, api) => {\n    patchQueueMicrotask(global, api);\n  });\n}\nfunction rollupMain() {\n  const Zone = loadZone();\n  patchNode(Zone); // Node needs to come first.\n  patchPromise(Zone);\n  patchToString(Zone);\n  return Zone;\n}\nrollupMain();"], "mappings": ";;;;;;;;AAOA,IAAM,SAAS;AAGf,SAAS,WAAW,MAAM;AACxB,QAAM,eAAe,OAAO,sBAAsB,KAAK;AACvD,SAAO,eAAe;AACxB;AACA,SAAS,WAAW;AAClB,QAAM,cAAc,OAAO,aAAa;AACxC,WAAS,KAAK,MAAM;AAClB,mBAAe,YAAY,MAAM,KAAK,YAAY,MAAM,EAAE,IAAI;AAAA,EAChE;AACA,WAAS,mBAAmB,MAAM,OAAO;AACvC,mBAAe,YAAY,SAAS,KAAK,YAAY,SAAS,EAAE,MAAM,KAAK;AAAA,EAC7E;AACA,OAAK,MAAM;AAAA,EACX,MAAM,SAAS;AAAA,IACb,OAAO,aAAa;AAAA,IACpB,OAAO,oBAAoB;AACzB,UAAI,OAAO,SAAS,MAAM,QAAQ,kBAAkB,GAAG;AACrD,cAAM,IAAI,MAAM,+RAAmT;AAAA,MACrU;AAAA,IACF;AAAA,IACA,WAAW,OAAO;AAChB,UAAI,OAAO,SAAS;AACpB,aAAO,KAAK,QAAQ;AAClB,eAAO,KAAK;AAAA,MACd;AACA,aAAO;AAAA,IACT;AAAA,IACA,WAAW,UAAU;AACnB,aAAO,kBAAkB;AAAA,IAC3B;AAAA,IACA,WAAW,cAAc;AACvB,aAAO;AAAA,IACT;AAAA,IACA,OAAO,aAAa,MAAM,IAAI,kBAAkB,OAAO;AACrD,UAAI,QAAQ,eAAe,IAAI,GAAG;AAIhC,cAAM,iBAAiB,OAAO,WAAW,yBAAyB,CAAC,MAAM;AACzE,YAAI,CAAC,mBAAmB,gBAAgB;AACtC,gBAAM,MAAM,2BAA2B,IAAI;AAAA,QAC7C;AAAA,MACF,WAAW,CAAC,OAAO,oBAAoB,IAAI,GAAG;AAC5C,cAAM,WAAW,UAAU;AAC3B,aAAK,QAAQ;AACb,gBAAQ,IAAI,IAAI,GAAG,QAAQ,UAAU,IAAI;AACzC,2BAAmB,UAAU,QAAQ;AAAA,MACvC;AAAA,IACF;AAAA,IACA,IAAI,SAAS;AACX,aAAO,KAAK;AAAA,IACd;AAAA,IACA,IAAI,OAAO;AACT,aAAO,KAAK;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,QAAQ,UAAU;AAC5B,WAAK,UAAU;AACf,WAAK,QAAQ,WAAW,SAAS,QAAQ,YAAY;AACrD,WAAK,cAAc,YAAY,SAAS,cAAc,CAAC;AACvD,WAAK,gBAAgB,IAAI,cAAc,MAAM,KAAK,WAAW,KAAK,QAAQ,eAAe,QAAQ;AAAA,IACnG;AAAA,IACA,IAAI,KAAK;AACP,YAAM,OAAO,KAAK,YAAY,GAAG;AACjC,UAAI,KAAM,QAAO,KAAK,YAAY,GAAG;AAAA,IACvC;AAAA,IACA,YAAY,KAAK;AACf,UAAI,UAAU;AACd,aAAO,SAAS;AACd,YAAI,QAAQ,YAAY,eAAe,GAAG,GAAG;AAC3C,iBAAO;AAAA,QACT;AACA,kBAAU,QAAQ;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAAA,IACA,KAAK,UAAU;AACb,UAAI,CAAC,SAAU,OAAM,IAAI,MAAM,oBAAoB;AACnD,aAAO,KAAK,cAAc,KAAK,MAAM,QAAQ;AAAA,IAC/C;AAAA,IACA,KAAK,UAAU,QAAQ;AACrB,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,MAAM,6BAA6B,QAAQ;AAAA,MACvD;AACA,YAAM,YAAY,KAAK,cAAc,UAAU,MAAM,UAAU,MAAM;AACrE,YAAM,OAAO;AACb,aAAO,WAAY;AACjB,eAAO,KAAK,WAAW,WAAW,MAAM,WAAW,MAAM;AAAA,MAC3D;AAAA,IACF;AAAA,IACA,IAAI,UAAU,WAAW,WAAW,QAAQ;AAC1C,0BAAoB;AAAA,QAClB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AACA,UAAI;AACF,eAAO,KAAK,cAAc,OAAO,MAAM,UAAU,WAAW,WAAW,MAAM;AAAA,MAC/E,UAAE;AACA,4BAAoB,kBAAkB;AAAA,MACxC;AAAA,IACF;AAAA,IACA,WAAW,UAAU,YAAY,MAAM,WAAW,QAAQ;AACxD,0BAAoB;AAAA,QAClB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AACA,UAAI;AACF,YAAI;AACF,iBAAO,KAAK,cAAc,OAAO,MAAM,UAAU,WAAW,WAAW,MAAM;AAAA,QAC/E,SAAS,OAAO;AACd,cAAI,KAAK,cAAc,YAAY,MAAM,KAAK,GAAG;AAC/C,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,UAAE;AACA,4BAAoB,kBAAkB;AAAA,MACxC;AAAA,IACF;AAAA,IACA,QAAQ,MAAM,WAAW,WAAW;AAClC,UAAI,KAAK,QAAQ,MAAM;AACrB,cAAM,IAAI,MAAM,iEAAiE,KAAK,QAAQ,SAAS,OAAO,kBAAkB,KAAK,OAAO,GAAG;AAAA,MACjJ;AACA,YAAM,WAAW;AAIjB,YAAM;AAAA,QACJ;AAAA,QACA,MAAM;AAAA,UACJ,aAAa;AAAA,UACb,gBAAgB;AAAA,QAClB,IAAI,CAAC;AAAA,MACP,IAAI;AACJ,UAAI,KAAK,UAAU,iBAAiB,SAAS,aAAa,SAAS,YAAY;AAC7E;AAAA,MACF;AACA,YAAM,eAAe,KAAK,SAAS;AACnC,sBAAgB,SAAS,cAAc,SAAS,SAAS;AACzD,YAAM,eAAe;AACrB,qBAAe;AACf,0BAAoB;AAAA,QAClB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AACA,UAAI;AACF,YAAI,QAAQ,aAAa,KAAK,QAAQ,CAAC,cAAc,CAAC,eAAe;AACnE,eAAK,WAAW;AAAA,QAClB;AACA,YAAI;AACF,iBAAO,KAAK,cAAc,WAAW,MAAM,UAAU,WAAW,SAAS;AAAA,QAC3E,SAAS,OAAO;AACd,cAAI,KAAK,cAAc,YAAY,MAAM,KAAK,GAAG;AAC/C,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,UAAE;AAGA,cAAM,QAAQ,KAAK;AACnB,YAAI,UAAU,gBAAgB,UAAU,SAAS;AAC/C,cAAI,QAAQ,aAAa,cAAc,iBAAiB,UAAU,YAAY;AAC5E,4BAAgB,SAAS,cAAc,WAAW,SAAS,UAAU;AAAA,UACvE,OAAO;AACL,kBAAM,gBAAgB,SAAS;AAC/B,iBAAK,iBAAiB,UAAU,EAAE;AAClC,4BAAgB,SAAS,cAAc,cAAc,SAAS,YAAY;AAC1E,gBAAI,eAAe;AACjB,uBAAS,iBAAiB;AAAA,YAC5B;AAAA,UACF;AAAA,QACF;AACA,4BAAoB,kBAAkB;AACtC,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,aAAa,MAAM;AACjB,UAAI,KAAK,QAAQ,KAAK,SAAS,MAAM;AAGnC,YAAI,UAAU;AACd,eAAO,SAAS;AACd,cAAI,YAAY,KAAK,MAAM;AACzB,kBAAM,MAAM,8BAA8B,KAAK,IAAI,8CAA8C,KAAK,KAAK,IAAI,EAAE;AAAA,UACnH;AACA,oBAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AACA,WAAK,cAAc,YAAY,YAAY;AAC3C,YAAM,gBAAgB,CAAC;AACvB,WAAK,iBAAiB;AACtB,WAAK,QAAQ;AACb,UAAI;AACF,eAAO,KAAK,cAAc,aAAa,MAAM,IAAI;AAAA,MACnD,SAAS,KAAK;AAGZ,aAAK,cAAc,SAAS,YAAY,YAAY;AAEpD,aAAK,cAAc,YAAY,MAAM,GAAG;AACxC,cAAM;AAAA,MACR;AACA,UAAI,KAAK,mBAAmB,eAAe;AAEzC,aAAK,iBAAiB,MAAM,CAAC;AAAA,MAC/B;AACA,UAAI,KAAK,SAAS,YAAY;AAC5B,aAAK,cAAc,WAAW,UAAU;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAAA,IACA,kBAAkB,QAAQ,UAAU,MAAM,gBAAgB;AACxD,aAAO,KAAK,aAAa,IAAI,SAAS,WAAW,QAAQ,UAAU,MAAM,gBAAgB,MAAS,CAAC;AAAA,IACrG;AAAA,IACA,kBAAkB,QAAQ,UAAU,MAAM,gBAAgB,cAAc;AACtE,aAAO,KAAK,aAAa,IAAI,SAAS,WAAW,QAAQ,UAAU,MAAM,gBAAgB,YAAY,CAAC;AAAA,IACxG;AAAA,IACA,kBAAkB,QAAQ,UAAU,MAAM,gBAAgB,cAAc;AACtE,aAAO,KAAK,aAAa,IAAI,SAAS,WAAW,QAAQ,UAAU,MAAM,gBAAgB,YAAY,CAAC;AAAA,IACxG;AAAA,IACA,WAAW,MAAM;AACf,UAAI,KAAK,QAAQ,KAAM,OAAM,IAAI,MAAM,uEAAuE,KAAK,QAAQ,SAAS,OAAO,kBAAkB,KAAK,OAAO,GAAG;AAC5K,UAAI,KAAK,UAAU,aAAa,KAAK,UAAU,SAAS;AACtD;AAAA,MACF;AACA,WAAK,cAAc,WAAW,WAAW,OAAO;AAChD,UAAI;AACF,aAAK,cAAc,WAAW,MAAM,IAAI;AAAA,MAC1C,SAAS,KAAK;AAEZ,aAAK,cAAc,SAAS,SAAS;AACrC,aAAK,cAAc,YAAY,MAAM,GAAG;AACxC,cAAM;AAAA,MACR;AACA,WAAK,iBAAiB,MAAM,EAAE;AAC9B,WAAK,cAAc,cAAc,SAAS;AAC1C,WAAK,WAAW;AAChB,aAAO;AAAA,IACT;AAAA,IACA,iBAAiB,MAAM,OAAO;AAC5B,YAAM,gBAAgB,KAAK;AAC3B,UAAI,SAAS,IAAI;AACf,aAAK,iBAAiB;AAAA,MACxB;AACA,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,sBAAc,CAAC,EAAE,iBAAiB,KAAK,MAAM,KAAK;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc;AAAA,IAClB,MAAM;AAAA,IACN,WAAW,CAAC,UAAU,GAAG,QAAQ,iBAAiB,SAAS,QAAQ,QAAQ,YAAY;AAAA,IACvF,gBAAgB,CAAC,UAAU,GAAG,QAAQ,SAAS,SAAS,aAAa,QAAQ,IAAI;AAAA,IACjF,cAAc,CAAC,UAAU,GAAG,QAAQ,MAAM,WAAW,cAAc,SAAS,WAAW,QAAQ,MAAM,WAAW,SAAS;AAAA,IACzH,cAAc,CAAC,UAAU,GAAG,QAAQ,SAAS,SAAS,WAAW,QAAQ,IAAI;AAAA,EAC/E;AAAA,EACA,MAAM,cAAc;AAAA,IAClB,IAAI,OAAO;AACT,aAAO,KAAK;AAAA,IACd;AAAA,IACA;AAAA,IACA,cAAc;AAAA,MACZ,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,MAAM,gBAAgB,UAAU;AAC1C,WAAK,QAAQ;AACb,WAAK,kBAAkB;AACvB,WAAK,UAAU,aAAa,YAAY,SAAS,SAAS,WAAW,eAAe;AACpF,WAAK,YAAY,aAAa,SAAS,SAAS,iBAAiB,eAAe;AAChF,WAAK,gBAAgB,aAAa,SAAS,SAAS,KAAK,QAAQ,eAAe;AAChF,WAAK,eAAe,aAAa,SAAS,cAAc,WAAW,eAAe;AAClF,WAAK,iBAAiB,aAAa,SAAS,cAAc,iBAAiB,eAAe;AAC1F,WAAK,qBAAqB,aAAa,SAAS,cAAc,KAAK,QAAQ,eAAe;AAC1F,WAAK,YAAY,aAAa,SAAS,WAAW,WAAW,eAAe;AAC5E,WAAK,cAAc,aAAa,SAAS,WAAW,iBAAiB,eAAe;AACpF,WAAK,kBAAkB,aAAa,SAAS,WAAW,KAAK,QAAQ,eAAe;AACpF,WAAK,iBAAiB,aAAa,SAAS,gBAAgB,WAAW,eAAe;AACtF,WAAK,mBAAmB,aAAa,SAAS,gBAAgB,iBAAiB,eAAe;AAC9F,WAAK,uBAAuB,aAAa,SAAS,gBAAgB,KAAK,QAAQ,eAAe;AAC9F,WAAK,kBAAkB,aAAa,SAAS,iBAAiB,WAAW,eAAe;AACxF,WAAK,oBAAoB,aAAa,SAAS,iBAAiB,iBAAiB,eAAe;AAChG,WAAK,wBAAwB,aAAa,SAAS,iBAAiB,KAAK,QAAQ,eAAe;AAChG,WAAK,gBAAgB,aAAa,SAAS,eAAe,WAAW,eAAe;AACpF,WAAK,kBAAkB,aAAa,SAAS,eAAe,iBAAiB,eAAe;AAC5F,WAAK,sBAAsB,aAAa,SAAS,eAAe,KAAK,QAAQ,eAAe;AAC5F,WAAK,gBAAgB,aAAa,SAAS,eAAe,WAAW,eAAe;AACpF,WAAK,kBAAkB,aAAa,SAAS,eAAe,iBAAiB,eAAe;AAC5F,WAAK,sBAAsB,aAAa,SAAS,eAAe,KAAK,QAAQ,eAAe;AAC5F,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,oBAAoB;AACzB,WAAK,mBAAmB;AACxB,YAAM,kBAAkB,YAAY,SAAS;AAC7C,YAAM,gBAAgB,kBAAkB,eAAe;AACvD,UAAI,mBAAmB,eAAe;AAGpC,aAAK,aAAa,kBAAkB,WAAW;AAC/C,aAAK,eAAe;AACpB,aAAK,oBAAoB;AACzB,aAAK,mBAAmB,KAAK;AAC7B,YAAI,CAAC,SAAS,gBAAgB;AAC5B,eAAK,kBAAkB;AACvB,eAAK,oBAAoB;AACzB,eAAK,wBAAwB,KAAK;AAAA,QACpC;AACA,YAAI,CAAC,SAAS,cAAc;AAC1B,eAAK,gBAAgB;AACrB,eAAK,kBAAkB;AACvB,eAAK,sBAAsB,KAAK;AAAA,QAClC;AACA,YAAI,CAAC,SAAS,cAAc;AAC1B,eAAK,gBAAgB;AACrB,eAAK,kBAAkB;AACvB,eAAK,sBAAsB,KAAK;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,IACA,KAAK,YAAY,UAAU;AACzB,aAAO,KAAK,UAAU,KAAK,QAAQ,OAAO,KAAK,WAAW,KAAK,MAAM,YAAY,QAAQ,IAAI,IAAI,SAAS,YAAY,QAAQ;AAAA,IAChI;AAAA,IACA,UAAU,YAAY,UAAU,QAAQ;AACtC,aAAO,KAAK,eAAe,KAAK,aAAa,YAAY,KAAK,gBAAgB,KAAK,oBAAoB,YAAY,UAAU,MAAM,IAAI;AAAA,IACzI;AAAA,IACA,OAAO,YAAY,UAAU,WAAW,WAAW,QAAQ;AACzD,aAAO,KAAK,YAAY,KAAK,UAAU,SAAS,KAAK,aAAa,KAAK,iBAAiB,YAAY,UAAU,WAAW,WAAW,MAAM,IAAI,SAAS,MAAM,WAAW,SAAS;AAAA,IACnL;AAAA,IACA,YAAY,YAAY,OAAO;AAC7B,aAAO,KAAK,iBAAiB,KAAK,eAAe,cAAc,KAAK,kBAAkB,KAAK,sBAAsB,YAAY,KAAK,IAAI;AAAA,IACxI;AAAA,IACA,aAAa,YAAY,MAAM;AAC7B,UAAI,aAAa;AACjB,UAAI,KAAK,iBAAiB;AACxB,YAAI,KAAK,YAAY;AACnB,qBAAW,eAAe,KAAK,KAAK,iBAAiB;AAAA,QACvD;AACA,qBAAa,KAAK,gBAAgB,eAAe,KAAK,mBAAmB,KAAK,uBAAuB,YAAY,IAAI;AACrH,YAAI,CAAC,WAAY,cAAa;AAAA,MAChC,OAAO;AACL,YAAI,KAAK,YAAY;AACnB,eAAK,WAAW,IAAI;AAAA,QACtB,WAAW,KAAK,QAAQ,WAAW;AACjC,4BAAkB,IAAI;AAAA,QACxB,OAAO;AACL,gBAAM,IAAI,MAAM,6BAA6B;AAAA,QAC/C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IACA,WAAW,YAAY,MAAM,WAAW,WAAW;AACjD,aAAO,KAAK,gBAAgB,KAAK,cAAc,aAAa,KAAK,iBAAiB,KAAK,qBAAqB,YAAY,MAAM,WAAW,SAAS,IAAI,KAAK,SAAS,MAAM,WAAW,SAAS;AAAA,IAChM;AAAA,IACA,WAAW,YAAY,MAAM;AAC3B,UAAI;AACJ,UAAI,KAAK,eAAe;AACtB,gBAAQ,KAAK,cAAc,aAAa,KAAK,iBAAiB,KAAK,qBAAqB,YAAY,IAAI;AAAA,MAC1G,OAAO;AACL,YAAI,CAAC,KAAK,UAAU;AAClB,gBAAM,MAAM,wBAAwB;AAAA,QACtC;AACA,gBAAQ,KAAK,SAAS,IAAI;AAAA,MAC5B;AACA,aAAO;AAAA,IACT;AAAA,IACA,QAAQ,YAAY,SAAS;AAG3B,UAAI;AACF,aAAK,cAAc,KAAK,WAAW,UAAU,KAAK,cAAc,KAAK,kBAAkB,YAAY,OAAO;AAAA,MAC5G,SAAS,KAAK;AACZ,aAAK,YAAY,YAAY,GAAG;AAAA,MAClC;AAAA,IACF;AAAA,IACA,iBAAiB,MAAM,OAAO;AAC5B,YAAM,SAAS,KAAK;AACpB,YAAM,OAAO,OAAO,IAAI;AACxB,YAAM,OAAO,OAAO,IAAI,IAAI,OAAO;AACnC,UAAI,OAAO,GAAG;AACZ,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AACA,UAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,cAAM,UAAU;AAAA,UACd,WAAW,OAAO,WAAW,IAAI;AAAA,UACjC,WAAW,OAAO,WAAW,IAAI;AAAA,UACjC,WAAW,OAAO,WAAW,IAAI;AAAA,UACjC,QAAQ;AAAA,QACV;AACA,aAAK,QAAQ,KAAK,OAAO,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,SAAS;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,YAAY,MAAM,QAAQ,UAAU,SAAS,YAAY,UAAU;AACjE,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,OAAO;AACZ,WAAK,aAAa;AAClB,WAAK,WAAW;AAChB,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AACA,WAAK,WAAW;AAChB,YAAMA,QAAO;AAEb,UAAI,SAAS,aAAa,WAAW,QAAQ,MAAM;AACjD,aAAK,SAAS,SAAS;AAAA,MACzB,OAAO;AACL,aAAK,SAAS,WAAY;AACxB,iBAAO,SAAS,WAAW,KAAK,QAAQA,OAAM,MAAM,SAAS;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAAA,IACA,OAAO,WAAW,MAAM,QAAQ,MAAM;AACpC,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AACA;AACA,UAAI;AACF,aAAK;AACL,eAAO,KAAK,KAAK,QAAQ,MAAM,QAAQ,IAAI;AAAA,MAC7C,UAAE;AACA,YAAI,6BAA6B,GAAG;AAClC,8BAAoB;AAAA,QACtB;AACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,IAAI,OAAO;AACT,aAAO,KAAK;AAAA,IACd;AAAA,IACA,IAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AAAA,IACA,wBAAwB;AACtB,WAAK,cAAc,cAAc,UAAU;AAAA,IAC7C;AAAA,IACA,cAAc,SAAS,YAAY,YAAY;AAC7C,UAAI,KAAK,WAAW,cAAc,KAAK,WAAW,YAAY;AAC5D,aAAK,SAAS;AACd,YAAI,WAAW,cAAc;AAC3B,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF,OAAO;AACL,cAAM,IAAI,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,6BAA6B,OAAO,uBAAuB,UAAU,IAAI,aAAa,UAAU,aAAa,MAAM,EAAE,UAAU,KAAK,MAAM,IAAI;AAAA,MAC5L;AAAA,IACF;AAAA,IACA,WAAW;AACT,UAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,aAAa,aAAa;AAC1D,eAAO,KAAK,KAAK,SAAS,SAAS;AAAA,MACrC,OAAO;AACL,eAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,MAC5C;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,SAAS;AACP,aAAO;AAAA,QACL,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAMA,QAAM,mBAAmB,WAAW,YAAY;AAChD,QAAM,gBAAgB,WAAW,SAAS;AAC1C,QAAM,aAAa,WAAW,MAAM;AACpC,MAAI,kBAAkB,CAAC;AACvB,MAAI,4BAA4B;AAChC,MAAI;AACJ,WAAS,wBAAwB,MAAM;AACrC,QAAI,CAAC,6BAA6B;AAChC,UAAI,OAAO,aAAa,GAAG;AACzB,sCAA8B,OAAO,aAAa,EAAE,QAAQ,CAAC;AAAA,MAC/D;AAAA,IACF;AACA,QAAI,6BAA6B;AAC/B,UAAI,aAAa,4BAA4B,UAAU;AACvD,UAAI,CAAC,YAAY;AAGf,qBAAa,4BAA4B,MAAM;AAAA,MACjD;AACA,iBAAW,KAAK,6BAA6B,IAAI;AAAA,IACnD,OAAO;AACL,aAAO,gBAAgB,EAAE,MAAM,CAAC;AAAA,IAClC;AAAA,EACF;AACA,WAAS,kBAAkB,MAAM;AAG/B,QAAI,8BAA8B,KAAK,gBAAgB,WAAW,GAAG;AAEnE,8BAAwB,mBAAmB;AAAA,IAC7C;AACA,YAAQ,gBAAgB,KAAK,IAAI;AAAA,EACnC;AACA,WAAS,sBAAsB;AAC7B,QAAI,CAAC,2BAA2B;AAC9B,kCAA4B;AAC5B,aAAO,gBAAgB,QAAQ;AAC7B,cAAM,QAAQ;AACd,0BAAkB,CAAC;AACnB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAM,OAAO,MAAM,CAAC;AACpB,cAAI;AACF,iBAAK,KAAK,QAAQ,MAAM,MAAM,IAAI;AAAA,UACpC,SAAS,OAAO;AACd,iBAAK,iBAAiB,KAAK;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AACA,WAAK,mBAAmB;AACxB,kCAA4B;AAAA,IAC9B;AAAA,EACF;AAMA,QAAM,UAAU;AAAA,IACd,MAAM;AAAA,EACR;AACA,QAAM,eAAe,gBACnB,aAAa,cACb,YAAY,aACZ,UAAU,WACV,YAAY,aACZ,UAAU;AACZ,QAAM,YAAY,aAChB,YAAY,aACZ,YAAY;AACd,QAAM,UAAU,CAAC;AACjB,QAAM,OAAO;AAAA,IACX,QAAQ;AAAA,IACR,kBAAkB,MAAM;AAAA,IACxB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB;AAAA,IACA,mBAAmB,MAAM,CAAC,SAAS,WAAW,iCAAiC,CAAC;AAAA,IAChF,kBAAkB,MAAM,CAAC;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa,MAAM;AAAA,IACnB,eAAe,MAAM,CAAC;AAAA,IACtB,WAAW,MAAM;AAAA,IACjB,gBAAgB,MAAM;AAAA,IACtB,qBAAqB,MAAM;AAAA,IAC3B,YAAY,MAAM;AAAA,IAClB,kBAAkB,MAAM;AAAA,IACxB,sBAAsB,MAAM;AAAA,IAC5B,gCAAgC,MAAM;AAAA,IACtC,cAAc,MAAM;AAAA,IACpB,YAAY,MAAM,CAAC;AAAA,IACnB,YAAY,MAAM;AAAA,IAClB,qBAAqB,MAAM;AAAA,IAC3B,kBAAkB,MAAM,CAAC;AAAA,IACzB,uBAAuB,MAAM;AAAA,IAC7B,mBAAmB,MAAM;AAAA,IACzB,gBAAgB,MAAM;AAAA,IACtB;AAAA,EACF;AACA,MAAI,oBAAoB;AAAA,IACtB,QAAQ;AAAA,IACR,MAAM,IAAI,SAAS,MAAM,IAAI;AAAA,EAC/B;AACA,MAAI,eAAe;AACnB,MAAI,4BAA4B;AAChC,WAAS,OAAO;AAAA,EAAC;AACjB,qBAAmB,QAAQ,MAAM;AACjC,SAAO;AACT;AAUA,IAAM,iCAAiC,OAAO;AAE9C,IAAM,uBAAuB,OAAO;AAEpC,IAAM,uBAAuB,OAAO;AAEpC,IAAM,aAAa,MAAM,UAAU;AAEnC,IAAM,yBAAyB;AAE/B,IAAM,4BAA4B;AAElC,IAAM,WAAW;AAEjB,IAAM,YAAY;AAElB,IAAM,qBAAqB,WAAW,EAAE;AACxC,SAAS,oBAAoB,UAAU,QAAQ;AAC7C,SAAO,KAAK,QAAQ,KAAK,UAAU,MAAM;AAC3C;AACA,SAAS,iCAAiC,QAAQ,UAAU,MAAM,gBAAgB,cAAc;AAC9F,SAAO,KAAK,QAAQ,kBAAkB,QAAQ,UAAU,MAAM,gBAAgB,YAAY;AAC5F;AACA,IAAM,aAAa;AACnB,IAAM,iBAAiB,OAAO,WAAW;AACzC,IAAM,iBAAiB,iBAAiB,SAAS;AACjD,IAAM,UAAU,kBAAkB,kBAAkB;AACpD,IAAM,mBAAmB;AACzB,SAAS,cAAc,MAAM,QAAQ;AACnC,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,QAAI,OAAO,KAAK,CAAC,MAAM,YAAY;AACjC,WAAK,CAAC,IAAI,oBAAoB,KAAK,CAAC,GAAG,SAAS,MAAM,CAAC;AAAA,IACzD;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,cAAc;AACxC,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,MAAI,aAAa,aAAa,OAAO;AACnC,WAAO;AAAA,EACT;AACA,SAAO,EAAE,OAAO,aAAa,QAAQ,cAAc,OAAO,aAAa,QAAQ;AACjF;AACA,IAAM,cAAc,OAAO,sBAAsB,eAAe,gBAAgB;AAGhF,IAAM,SAAS,EAAE,QAAQ,YAAY,OAAO,QAAQ,YAAY,eAAe,QAAQ,QAAQ,SAAS,MAAM;AAC9G,IAAM,YAAY,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,kBAAkB,eAAe,aAAa;AAI9F,IAAM,QAAQ,OAAO,QAAQ,YAAY,eAAe,QAAQ,QAAQ,SAAS,MAAM,sBAAsB,CAAC,eAAe,CAAC,EAAE,kBAAkB,eAAe,aAAa;AAC9K,IAAM,yBAAyB,CAAC;AAChC,IAAM,2BAA2B,WAAW,qBAAqB;AACjE,IAAM,SAAS,SAAU,OAAO;AAG9B,UAAQ,SAAS,QAAQ;AACzB,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,MAAI,kBAAkB,uBAAuB,MAAM,IAAI;AACvD,MAAI,CAAC,iBAAiB;AACpB,sBAAkB,uBAAuB,MAAM,IAAI,IAAI,WAAW,gBAAgB,MAAM,IAAI;AAAA,EAC9F;AACA,QAAM,SAAS,QAAQ,MAAM,UAAU;AACvC,QAAM,WAAW,OAAO,eAAe;AACvC,MAAI;AACJ,MAAI,aAAa,WAAW,kBAAkB,MAAM,SAAS,SAAS;AAIpE,UAAM,aAAa;AACnB,aAAS,YAAY,SAAS,KAAK,MAAM,WAAW,SAAS,WAAW,UAAU,WAAW,QAAQ,WAAW,OAAO,WAAW,KAAK;AACvI,QAAI,WAAW,MAAM;AACnB,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,OAAO;AACL,aAAS,YAAY,SAAS,MAAM,MAAM,SAAS;AACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAM,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,MAMf,QAAQ,wBAAwB;AAAA;AAAA,MAGhC,OAAO,WAAW;AAAA,MAAU;AAC1B,YAAM,cAAc;AAAA,IACtB,WAAW,UAAU,UAAa,CAAC,QAAQ;AACzC,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,cAAc,KAAK,MAAM,WAAW;AAC3C,MAAI,OAAO,+BAA+B,KAAK,IAAI;AACnD,MAAI,CAAC,QAAQ,WAAW;AAEtB,UAAM,gBAAgB,+BAA+B,WAAW,IAAI;AACpE,QAAI,eAAe;AACjB,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAGA,MAAI,CAAC,QAAQ,CAAC,KAAK,cAAc;AAC/B;AAAA,EACF;AACA,QAAM,sBAAsB,WAAW,OAAO,OAAO,SAAS;AAC9D,MAAI,IAAI,eAAe,mBAAmB,KAAK,IAAI,mBAAmB,GAAG;AACvE;AAAA,EACF;AAMA,SAAO,KAAK;AACZ,SAAO,KAAK;AACZ,QAAM,kBAAkB,KAAK;AAC7B,QAAM,kBAAkB,KAAK;AAE7B,QAAM,YAAY,KAAK,MAAM,CAAC;AAC9B,MAAI,kBAAkB,uBAAuB,SAAS;AACtD,MAAI,CAAC,iBAAiB;AACpB,sBAAkB,uBAAuB,SAAS,IAAI,WAAW,gBAAgB,SAAS;AAAA,EAC5F;AACA,OAAK,MAAM,SAAU,UAAU;AAK7B,QAAI,SAAS;AACb,QAAI,CAAC,UAAU,QAAQ,SAAS;AAC9B,eAAS;AAAA,IACX;AACA,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,gBAAgB,OAAO,eAAe;AAC5C,QAAI,OAAO,kBAAkB,YAAY;AACvC,aAAO,oBAAoB,WAAW,MAAM;AAAA,IAC9C;AAIA,qBAAiB,KAAK,QAAQ,IAAI;AAClC,WAAO,eAAe,IAAI;AAC1B,QAAI,OAAO,aAAa,YAAY;AAClC,aAAO,iBAAiB,WAAW,QAAQ,KAAK;AAAA,IAClD;AAAA,EACF;AAGA,OAAK,MAAM,WAAY;AAGrB,QAAI,SAAS;AACb,QAAI,CAAC,UAAU,QAAQ,SAAS;AAC9B,eAAS;AAAA,IACX;AACA,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,UAAM,WAAW,OAAO,eAAe;AACvC,QAAI,UAAU;AACZ,aAAO;AAAA,IACT,WAAW,iBAAiB;AAO1B,UAAI,QAAQ,gBAAgB,KAAK,IAAI;AACrC,UAAI,OAAO;AACT,aAAK,IAAI,KAAK,MAAM,KAAK;AACzB,YAAI,OAAO,OAAO,gBAAgB,MAAM,YAAY;AAClD,iBAAO,gBAAgB,IAAI;AAAA,QAC7B;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,uBAAqB,KAAK,MAAM,IAAI;AACpC,MAAI,mBAAmB,IAAI;AAC7B;AACA,SAAS,kBAAkB,KAAK,YAAY,WAAW;AACrD,MAAI,YAAY;AACd,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,oBAAc,KAAK,OAAO,WAAW,CAAC,GAAG,SAAS;AAAA,IACpD;AAAA,EACF,OAAO;AACL,UAAM,eAAe,CAAC;AACtB,eAAW,QAAQ,KAAK;AACtB,UAAI,KAAK,MAAM,GAAG,CAAC,KAAK,MAAM;AAC5B,qBAAa,KAAK,IAAI;AAAA,MACxB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,oBAAc,KAAK,aAAa,CAAC,GAAG,SAAS;AAAA,IAC/C;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,KAAK,MAAM;AACvC,MAAI,OAAO,OAAO,0BAA0B,YAAY;AACtD;AAAA,EACF;AACA,QAAM,UAAU,OAAO,sBAAsB,GAAG;AAChD,UAAQ,QAAQ,YAAU;AACxB,UAAM,OAAO,OAAO,yBAAyB,KAAK,MAAM;AACxD,WAAO,eAAe,MAAM,QAAQ;AAAA,MAClC,KAAK,WAAY;AACf,eAAO,IAAI,MAAM;AAAA,MACnB;AAAA,MACA,KAAK,SAAU,OAAO;AACpB,YAAI,SAAS,CAAC,KAAK,YAAY,OAAO,KAAK,QAAQ,aAAa;AAE9D;AAAA,QACF;AACA,YAAI,MAAM,IAAI;AAAA,MAChB;AAAA,MACA,YAAY,OAAO,KAAK,aAAa;AAAA,MACrC,cAAc,OAAO,KAAK,eAAe;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,6BAA6B;AACjC,SAAS,8BAA8B,MAAM;AAC3C,+BAA6B;AAC/B;AACA,SAAS,YAAY,QAAQ,MAAM,SAAS;AAC1C,MAAI,QAAQ;AACZ,SAAO,SAAS,CAAC,MAAM,eAAe,IAAI,GAAG;AAC3C,YAAQ,qBAAqB,KAAK;AAAA,EACpC;AACA,MAAI,CAAC,SAAS,OAAO,IAAI,GAAG;AAE1B,YAAQ;AAAA,EACV;AACA,QAAM,eAAe,WAAW,IAAI;AACpC,MAAI,WAAW;AACf,MAAI,UAAU,EAAE,WAAW,MAAM,YAAY,MAAM,CAAC,MAAM,eAAe,YAAY,IAAI;AACvF,eAAW,MAAM,YAAY,IAAI,MAAM,IAAI;AAG3C,UAAM,OAAO,SAAS,+BAA+B,OAAO,IAAI;AAChE,QAAI,mBAAmB,IAAI,GAAG;AAC5B,YAAM,gBAAgB,QAAQ,UAAU,cAAc,IAAI;AAC1D,YAAM,IAAI,IAAI,WAAY;AACxB,eAAO,cAAc,MAAM,SAAS;AAAA,MACtC;AACA,4BAAsB,MAAM,IAAI,GAAG,QAAQ;AAC3C,UAAI,4BAA4B;AAC9B,6BAAqB,UAAU,MAAM,IAAI,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,eAAe,KAAK,UAAU,aAAa;AAClD,MAAI,YAAY;AAChB,WAAS,aAAa,MAAM;AAC1B,UAAM,OAAO,KAAK;AAClB,SAAK,KAAK,KAAK,KAAK,IAAI,WAAY;AAClC,WAAK,OAAO,MAAM,MAAM,SAAS;AAAA,IACnC;AACA,cAAU,MAAM,KAAK,QAAQ,KAAK,IAAI;AACtC,WAAO;AAAA,EACT;AACA,cAAY,YAAY,KAAK,UAAU,cAAY,SAAUA,OAAM,MAAM;AACvE,UAAM,OAAO,YAAYA,OAAM,IAAI;AACnC,QAAI,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY;AAC7D,aAAO,iCAAiC,KAAK,MAAM,KAAK,KAAK,KAAK,GAAG,MAAM,YAAY;AAAA,IACzF,OAAO;AAEL,aAAO,SAAS,MAAMA,OAAM,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AACH;AACA,SAAS,eAAe,KAAK,UAAU,aAAa;AAClD,MAAI,YAAY;AAChB,WAAS,aAAa,MAAM;AAC1B,UAAM,OAAO,KAAK;AAClB,SAAK,KAAK,KAAK,KAAK,IAAI,WAAY;AAClC,WAAK,OAAO,MAAM,MAAM,SAAS;AAAA,IACnC;AACA,cAAU,MAAM,KAAK,QAAQ,KAAK,IAAI;AACtC,WAAO;AAAA,EACT;AACA,cAAY,YAAY,KAAK,UAAU,cAAY,SAAUA,OAAM,MAAM;AACvE,UAAM,OAAO,YAAYA,OAAM,IAAI;AACnC,QAAI,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY;AAC7D,aAAO,KAAK,QAAQ,kBAAkB,KAAK,MAAM,KAAK,KAAK,KAAK,GAAG,MAAM,YAAY;AAAA,IACvF,OAAO;AAEL,aAAO,SAAS,MAAMA,OAAM,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AACH;AACA,SAAS,sBAAsB,SAAS,UAAU;AAChD,UAAQ,WAAW,kBAAkB,CAAC,IAAI;AAC5C;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,aAAaC,OAAM;AAC1B,EAAAA,MAAK,aAAa,oBAAoB,CAACC,SAAQD,OAAM,QAAQ;AAC3D,UAAME,kCAAiC,OAAO;AAC9C,UAAMC,wBAAuB,OAAO;AACpC,aAAS,uBAAuB,KAAK;AACnC,UAAI,OAAO,IAAI,aAAa,OAAO,UAAU,UAAU;AACrD,cAAM,YAAY,IAAI,eAAe,IAAI,YAAY;AACrD,gBAAQ,YAAY,YAAY,MAAM,OAAO,KAAK,UAAU,GAAG;AAAA,MACjE;AACA,aAAO,MAAM,IAAI,SAAS,IAAI,OAAO,UAAU,SAAS,KAAK,GAAG;AAAA,IAClE;AACA,UAAMC,cAAa,IAAI;AACvB,UAAM,yBAAyB,CAAC;AAChC,UAAM,4CAA4CH,QAAOG,YAAW,6CAA6C,CAAC,MAAM;AACxH,UAAM,gBAAgBA,YAAW,SAAS;AAC1C,UAAM,aAAaA,YAAW,MAAM;AACpC,UAAM,gBAAgB;AACtB,QAAI,mBAAmB,OAAK;AAC1B,UAAI,IAAI,kBAAkB,GAAG;AAC3B,cAAM,YAAY,KAAK,EAAE;AACzB,YAAI,WAAW;AACb,kBAAQ,MAAM,gCAAgC,qBAAqB,QAAQ,UAAU,UAAU,WAAW,WAAW,EAAE,KAAK,MAAM,WAAW,EAAE,QAAQ,EAAE,KAAK,QAAQ,YAAY,WAAW,qBAAqB,QAAQ,UAAU,QAAQ,MAAS;AAAA,QACvP,OAAO;AACL,kBAAQ,MAAM,CAAC;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,QAAI,qBAAqB,MAAM;AAC7B,aAAO,uBAAuB,QAAQ;AACpC,cAAM,uBAAuB,uBAAuB,MAAM;AAC1D,YAAI;AACF,+BAAqB,KAAK,WAAW,MAAM;AACzC,gBAAI,qBAAqB,eAAe;AACtC,oBAAM,qBAAqB;AAAA,YAC7B;AACA,kBAAM;AAAA,UACR,CAAC;AAAA,QACH,SAAS,OAAO;AACd,mCAAyB,KAAK;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,UAAM,6CAA6CA,YAAW,kCAAkC;AAChG,aAAS,yBAAyB,GAAG;AACnC,UAAI,iBAAiB,CAAC;AACtB,UAAI;AACF,cAAM,UAAUJ,MAAK,0CAA0C;AAC/D,YAAI,OAAO,YAAY,YAAY;AACjC,kBAAQ,KAAK,MAAM,CAAC;AAAA,QACtB;AAAA,MACF,SAAS,KAAK;AAAA,MAAC;AAAA,IACjB;AACA,aAAS,WAAW,OAAO;AACzB,aAAO,SAAS,OAAO,MAAM,SAAS;AAAA,IACxC;AACA,aAAS,kBAAkB,OAAO;AAChC,aAAO;AAAA,IACT;AACA,aAAS,iBAAiB,WAAW;AACnC,aAAO,iBAAiB,OAAO,SAAS;AAAA,IAC1C;AACA,UAAM,cAAcI,YAAW,OAAO;AACtC,UAAM,cAAcA,YAAW,OAAO;AACtC,UAAM,gBAAgBA,YAAW,SAAS;AAC1C,UAAM,2BAA2BA,YAAW,oBAAoB;AAChE,UAAM,2BAA2BA,YAAW,oBAAoB;AAChE,UAAM,SAAS;AACf,UAAM,aAAa;AACnB,UAAM,WAAW;AACjB,UAAM,WAAW;AACjB,UAAM,oBAAoB;AAC1B,aAAS,aAAa,SAAS,OAAO;AACpC,aAAO,OAAK;AACV,YAAI;AACF,yBAAe,SAAS,OAAO,CAAC;AAAA,QAClC,SAAS,KAAK;AACZ,yBAAe,SAAS,OAAO,GAAG;AAAA,QACpC;AAAA,MAEF;AAAA,IACF;AACA,UAAM,OAAO,WAAY;AACvB,UAAI,YAAY;AAChB,aAAO,SAAS,QAAQ,iBAAiB;AACvC,eAAO,WAAY;AACjB,cAAI,WAAW;AACb;AAAA,UACF;AACA,sBAAY;AACZ,0BAAgB,MAAM,MAAM,SAAS;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa;AACnB,UAAM,4BAA4BA,YAAW,kBAAkB;AAE/D,aAAS,eAAe,SAAS,OAAO,OAAO;AAC7C,YAAM,cAAc,KAAK;AACzB,UAAI,YAAY,OAAO;AACrB,cAAM,IAAI,UAAU,UAAU;AAAA,MAChC;AACA,UAAI,QAAQ,WAAW,MAAM,YAAY;AAEvC,YAAI,OAAO;AACX,YAAI;AACF,cAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY;AAC5D,mBAAO,SAAS,MAAM;AAAA,UACxB;AAAA,QACF,SAAS,KAAK;AACZ,sBAAY,MAAM;AAChB,2BAAe,SAAS,OAAO,GAAG;AAAA,UACpC,CAAC,EAAE;AACH,iBAAO;AAAA,QACT;AAEA,YAAI,UAAU,YAAY,iBAAiB,oBAAoB,MAAM,eAAe,WAAW,KAAK,MAAM,eAAe,WAAW,KAAK,MAAM,WAAW,MAAM,YAAY;AAC1K,+BAAqB,KAAK;AAC1B,yBAAe,SAAS,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC;AAAA,QAChE,WAAW,UAAU,YAAY,OAAO,SAAS,YAAY;AAC3D,cAAI;AACF,iBAAK,KAAK,OAAO,YAAY,aAAa,SAAS,KAAK,CAAC,GAAG,YAAY,aAAa,SAAS,KAAK,CAAC,CAAC;AAAA,UACvG,SAAS,KAAK;AACZ,wBAAY,MAAM;AAChB,6BAAe,SAAS,OAAO,GAAG;AAAA,YACpC,CAAC,EAAE;AAAA,UACL;AAAA,QACF,OAAO;AACL,kBAAQ,WAAW,IAAI;AACvB,gBAAM,QAAQ,QAAQ,WAAW;AACjC,kBAAQ,WAAW,IAAI;AACvB,cAAI,QAAQ,aAAa,MAAM,eAAe;AAE5C,gBAAI,UAAU,UAAU;AAGtB,sBAAQ,WAAW,IAAI,QAAQ,wBAAwB;AACvD,sBAAQ,WAAW,IAAI,QAAQ,wBAAwB;AAAA,YACzD;AAAA,UACF;AAGA,cAAI,UAAU,YAAY,iBAAiB,OAAO;AAEhD,kBAAM,QAAQJ,MAAK,eAAeA,MAAK,YAAY,QAAQA,MAAK,YAAY,KAAK,aAAa;AAC9F,gBAAI,OAAO;AAET,cAAAG,sBAAqB,OAAO,2BAA2B;AAAA,gBACrD,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,OAAO;AAAA,cACT,CAAC;AAAA,YACH;AAAA,UACF;AACA,mBAAS,IAAI,GAAG,IAAI,MAAM,UAAS;AACjC,oCAAwB,SAAS,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC;AAAA,UACjF;AACA,cAAI,MAAM,UAAU,KAAK,SAAS,UAAU;AAC1C,oBAAQ,WAAW,IAAI;AACvB,gBAAI,uBAAuB;AAC3B,gBAAI;AAIF,oBAAM,IAAI,MAAM,4BAA4B,uBAAuB,KAAK,KAAK,SAAS,MAAM,QAAQ,OAAO,MAAM,QAAQ,GAAG;AAAA,YAC9H,SAAS,KAAK;AACZ,qCAAuB;AAAA,YACzB;AACA,gBAAI,2CAA2C;AAG7C,mCAAqB,gBAAgB;AAAA,YACvC;AACA,iCAAqB,YAAY;AACjC,iCAAqB,UAAU;AAC/B,iCAAqB,OAAOH,MAAK;AACjC,iCAAqB,OAAOA,MAAK;AACjC,mCAAuB,KAAK,oBAAoB;AAChD,gBAAI,kBAAkB;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AACA,UAAM,4BAA4BI,YAAW,yBAAyB;AACtE,aAAS,qBAAqB,SAAS;AACrC,UAAI,QAAQ,WAAW,MAAM,mBAAmB;AAM9C,YAAI;AACF,gBAAM,UAAUJ,MAAK,yBAAyB;AAC9C,cAAI,WAAW,OAAO,YAAY,YAAY;AAC5C,oBAAQ,KAAK,MAAM;AAAA,cACjB,WAAW,QAAQ,WAAW;AAAA,cAC9B;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,SAAS,KAAK;AAAA,QAAC;AACf,gBAAQ,WAAW,IAAI;AACvB,iBAAS,IAAI,GAAG,IAAI,uBAAuB,QAAQ,KAAK;AACtD,cAAI,YAAY,uBAAuB,CAAC,EAAE,SAAS;AACjD,mCAAuB,OAAO,GAAG,CAAC;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAAS,wBAAwB,SAAS,MAAM,cAAc,aAAa,YAAY;AACrF,2BAAqB,OAAO;AAC5B,YAAM,eAAe,QAAQ,WAAW;AACxC,YAAM,WAAW,eAAe,OAAO,gBAAgB,aAAa,cAAc,oBAAoB,OAAO,eAAe,aAAa,aAAa;AACtJ,WAAK,kBAAkB,QAAQ,MAAM;AACnC,YAAI;AACF,gBAAM,qBAAqB,QAAQ,WAAW;AAC9C,gBAAM,mBAAmB,CAAC,CAAC,gBAAgB,kBAAkB,aAAa,aAAa;AACvF,cAAI,kBAAkB;AAEpB,yBAAa,wBAAwB,IAAI;AACzC,yBAAa,wBAAwB,IAAI;AAAA,UAC3C;AAEA,gBAAM,QAAQ,KAAK,IAAI,UAAU,QAAW,oBAAoB,aAAa,oBAAoB,aAAa,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC;AAC3J,yBAAe,cAAc,MAAM,KAAK;AAAA,QAC1C,SAAS,OAAO;AAEd,yBAAe,cAAc,OAAO,KAAK;AAAA,QAC3C;AAAA,MACF,GAAG,YAAY;AAAA,IACjB;AACA,UAAM,+BAA+B;AACrC,UAAM,OAAO,WAAY;AAAA,IAAC;AAC1B,UAAM,iBAAiBC,QAAO;AAAA,IAC9B,MAAM,iBAAiB;AAAA,MACrB,OAAO,WAAW;AAChB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,QAAQ,OAAO;AACpB,YAAI,iBAAiB,kBAAkB;AACrC,iBAAO;AAAA,QACT;AACA,eAAO,eAAe,IAAI,KAAK,IAAI,GAAG,UAAU,KAAK;AAAA,MACvD;AAAA,MACA,OAAO,OAAO,OAAO;AACnB,eAAO,eAAe,IAAI,KAAK,IAAI,GAAG,UAAU,KAAK;AAAA,MACvD;AAAA,MACA,OAAO,gBAAgB;AACrB,cAAM,SAAS,CAAC;AAChB,eAAO,UAAU,IAAI,iBAAiB,CAAC,KAAK,QAAQ;AAClD,iBAAO,UAAU;AACjB,iBAAO,SAAS;AAAA,QAClB,CAAC;AACD,eAAO;AAAA,MACT;AAAA,MACA,OAAO,IAAI,QAAQ;AACjB,YAAI,CAAC,UAAU,OAAO,OAAO,OAAO,QAAQ,MAAM,YAAY;AAC5D,iBAAO,QAAQ,OAAO,IAAI,eAAe,CAAC,GAAG,4BAA4B,CAAC;AAAA,QAC5E;AACA,cAAM,WAAW,CAAC;AAClB,YAAI,QAAQ;AACZ,YAAI;AACF,mBAAS,KAAK,QAAQ;AACpB;AACA,qBAAS,KAAK,iBAAiB,QAAQ,CAAC,CAAC;AAAA,UAC3C;AAAA,QACF,SAAS,KAAK;AACZ,iBAAO,QAAQ,OAAO,IAAI,eAAe,CAAC,GAAG,4BAA4B,CAAC;AAAA,QAC5E;AACA,YAAI,UAAU,GAAG;AACf,iBAAO,QAAQ,OAAO,IAAI,eAAe,CAAC,GAAG,4BAA4B,CAAC;AAAA,QAC5E;AACA,YAAI,WAAW;AACf,cAAM,SAAS,CAAC;AAChB,eAAO,IAAI,iBAAiB,CAAC,SAAS,WAAW;AAC/C,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,qBAAS,CAAC,EAAE,KAAK,OAAK;AACpB,kBAAI,UAAU;AACZ;AAAA,cACF;AACA,yBAAW;AACX,sBAAQ,CAAC;AAAA,YACX,GAAG,SAAO;AACR,qBAAO,KAAK,GAAG;AACf;AACA,kBAAI,UAAU,GAAG;AACf,2BAAW;AACX,uBAAO,IAAI,eAAe,QAAQ,4BAA4B,CAAC;AAAA,cACjE;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,OAAO,KAAK,QAAQ;AAClB,YAAI;AACJ,YAAI;AACJ,YAAI,UAAU,IAAI,KAAK,CAAC,KAAK,QAAQ;AACnC,oBAAU;AACV,mBAAS;AAAA,QACX,CAAC;AACD,iBAAS,UAAU,OAAO;AACxB,kBAAQ,KAAK;AAAA,QACf;AACA,iBAAS,SAAS,OAAO;AACvB,iBAAO,KAAK;AAAA,QACd;AACA,iBAAS,SAAS,QAAQ;AACxB,cAAI,CAAC,WAAW,KAAK,GAAG;AACtB,oBAAQ,KAAK,QAAQ,KAAK;AAAA,UAC5B;AACA,gBAAM,KAAK,WAAW,QAAQ;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AAAA,MACA,OAAO,IAAI,QAAQ;AACjB,eAAO,iBAAiB,gBAAgB,MAAM;AAAA,MAChD;AAAA,MACA,OAAO,WAAW,QAAQ;AACxB,cAAM,IAAI,QAAQ,KAAK,qBAAqB,mBAAmB,OAAO;AACtE,eAAO,EAAE,gBAAgB,QAAQ;AAAA,UAC/B,cAAc,YAAU;AAAA,YACtB,QAAQ;AAAA,YACR;AAAA,UACF;AAAA,UACA,eAAe,UAAQ;AAAA,YACrB,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,OAAO,gBAAgB,QAAQ,UAAU;AACvC,YAAI;AACJ,YAAI;AACJ,YAAI,UAAU,IAAI,KAAK,CAAC,KAAK,QAAQ;AACnC,oBAAU;AACV,mBAAS;AAAA,QACX,CAAC;AAED,YAAI,kBAAkB;AACtB,YAAI,aAAa;AACjB,cAAM,iBAAiB,CAAC;AACxB,iBAAS,SAAS,QAAQ;AACxB,cAAI,CAAC,WAAW,KAAK,GAAG;AACtB,oBAAQ,KAAK,QAAQ,KAAK;AAAA,UAC5B;AACA,gBAAM,gBAAgB;AACtB,cAAI;AACF,kBAAM,KAAK,CAAAI,WAAS;AAClB,6BAAe,aAAa,IAAI,WAAW,SAAS,aAAaA,MAAK,IAAIA;AAC1E;AACA,kBAAI,oBAAoB,GAAG;AACzB,wBAAQ,cAAc;AAAA,cACxB;AAAA,YACF,GAAG,SAAO;AACR,kBAAI,CAAC,UAAU;AACb,uBAAO,GAAG;AAAA,cACZ,OAAO;AACL,+BAAe,aAAa,IAAI,SAAS,cAAc,GAAG;AAC1D;AACA,oBAAI,oBAAoB,GAAG;AACzB,0BAAQ,cAAc;AAAA,gBACxB;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH,SAAS,SAAS;AAChB,mBAAO,OAAO;AAAA,UAChB;AACA;AACA;AAAA,QACF;AAEA,2BAAmB;AACnB,YAAI,oBAAoB,GAAG;AACzB,kBAAQ,cAAc;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AAAA,MACA,YAAY,UAAU;AACpB,cAAM,UAAU;AAChB,YAAI,EAAE,mBAAmB,mBAAmB;AAC1C,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AACA,gBAAQ,WAAW,IAAI;AACvB,gBAAQ,WAAW,IAAI,CAAC;AACxB,YAAI;AACF,gBAAM,cAAc,KAAK;AACzB,sBAAY,SAAS,YAAY,aAAa,SAAS,QAAQ,CAAC,GAAG,YAAY,aAAa,SAAS,QAAQ,CAAC,CAAC;AAAA,QACjH,SAAS,OAAO;AACd,yBAAe,SAAS,OAAO,KAAK;AAAA,QACtC;AAAA,MACF;AAAA,MACA,KAAK,OAAO,WAAW,IAAI;AACzB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,OAAO,OAAO,IAAI;AACrB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,aAAa,YAAY;AAS5B,YAAI,IAAI,KAAK,cAAc,OAAO,OAAO;AACzC,YAAI,CAAC,KAAK,OAAO,MAAM,YAAY;AACjC,cAAI,KAAK,eAAe;AAAA,QAC1B;AACA,cAAM,eAAe,IAAI,EAAE,IAAI;AAC/B,cAAM,OAAOL,MAAK;AAClB,YAAI,KAAK,WAAW,KAAK,YAAY;AACnC,eAAK,WAAW,EAAE,KAAK,MAAM,cAAc,aAAa,UAAU;AAAA,QACpE,OAAO;AACL,kCAAwB,MAAM,MAAM,cAAc,aAAa,UAAU;AAAA,QAC3E;AACA,eAAO;AAAA,MACT;AAAA,MACA,MAAM,YAAY;AAChB,eAAO,KAAK,KAAK,MAAM,UAAU;AAAA,MACnC;AAAA,MACA,QAAQ,WAAW;AAEjB,YAAI,IAAI,KAAK,cAAc,OAAO,OAAO;AACzC,YAAI,CAAC,KAAK,OAAO,MAAM,YAAY;AACjC,cAAI;AAAA,QACN;AACA,cAAM,eAAe,IAAI,EAAE,IAAI;AAC/B,qBAAa,aAAa,IAAI;AAC9B,cAAM,OAAOA,MAAK;AAClB,YAAI,KAAK,WAAW,KAAK,YAAY;AACnC,eAAK,WAAW,EAAE,KAAK,MAAM,cAAc,WAAW,SAAS;AAAA,QACjE,OAAO;AACL,kCAAwB,MAAM,MAAM,cAAc,WAAW,SAAS;AAAA,QACxE;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAGA,qBAAiB,SAAS,IAAI,iBAAiB;AAC/C,qBAAiB,QAAQ,IAAI,iBAAiB;AAC9C,qBAAiB,MAAM,IAAI,iBAAiB;AAC5C,qBAAiB,KAAK,IAAI,iBAAiB;AAC3C,UAAM,gBAAgBC,QAAO,aAAa,IAAIA,QAAO,SAAS;AAC9D,IAAAA,QAAO,SAAS,IAAI;AACpB,UAAM,oBAAoBG,YAAW,aAAa;AAClD,aAAS,UAAU,MAAM;AACvB,YAAM,QAAQ,KAAK;AACnB,YAAM,OAAOF,gCAA+B,OAAO,MAAM;AACzD,UAAI,SAAS,KAAK,aAAa,SAAS,CAAC,KAAK,eAAe;AAG3D;AAAA,MACF;AACA,YAAM,eAAe,MAAM;AAE3B,YAAM,UAAU,IAAI;AACpB,WAAK,UAAU,OAAO,SAAU,WAAW,UAAU;AACnD,cAAM,UAAU,IAAI,iBAAiB,CAAC,SAAS,WAAW;AACxD,uBAAa,KAAK,MAAM,SAAS,MAAM;AAAA,QACzC,CAAC;AACD,eAAO,QAAQ,KAAK,WAAW,QAAQ;AAAA,MACzC;AACA,WAAK,iBAAiB,IAAI;AAAA,IAC5B;AACA,QAAI,YAAY;AAChB,aAAS,QAAQ,IAAI;AACnB,aAAO,SAAUH,OAAM,MAAM;AAC3B,YAAI,gBAAgB,GAAG,MAAMA,OAAM,IAAI;AACvC,YAAI,yBAAyB,kBAAkB;AAC7C,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,cAAc;AACzB,YAAI,CAAC,KAAK,iBAAiB,GAAG;AAC5B,oBAAU,IAAI;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,eAAe;AACjB,gBAAU,aAAa;AACvB,kBAAYE,SAAQ,SAAS,cAAY,QAAQ,QAAQ,CAAC;AAAA,IAC5D;AAEA,YAAQD,MAAK,WAAW,uBAAuB,CAAC,IAAI;AACpD,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,cAAcA,OAAM;AAG3B,EAAAA,MAAK,aAAa,YAAY,CAAAC,YAAU;AAEtC,UAAM,2BAA2B,SAAS,UAAU;AACpD,UAAM,2BAA2B,WAAW,kBAAkB;AAC9D,UAAM,iBAAiB,WAAW,SAAS;AAC3C,UAAM,eAAe,WAAW,OAAO;AACvC,UAAM,sBAAsB,SAAS,WAAW;AAC9C,UAAI,OAAO,SAAS,YAAY;AAC9B,cAAM,mBAAmB,KAAK,wBAAwB;AACtD,YAAI,kBAAkB;AACpB,cAAI,OAAO,qBAAqB,YAAY;AAC1C,mBAAO,yBAAyB,KAAK,gBAAgB;AAAA,UACvD,OAAO;AACL,mBAAO,OAAO,UAAU,SAAS,KAAK,gBAAgB;AAAA,UACxD;AAAA,QACF;AACA,YAAI,SAAS,SAAS;AACpB,gBAAM,gBAAgBA,QAAO,cAAc;AAC3C,cAAI,eAAe;AACjB,mBAAO,yBAAyB,KAAK,aAAa;AAAA,UACpD;AAAA,QACF;AACA,YAAI,SAAS,OAAO;AAClB,gBAAM,cAAcA,QAAO,YAAY;AACvC,cAAI,aAAa;AACf,mBAAO,yBAAyB,KAAK,WAAW;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AACA,aAAO,yBAAyB,KAAK,IAAI;AAAA,IAC3C;AACA,wBAAoB,wBAAwB,IAAI;AAChD,aAAS,UAAU,WAAW;AAE9B,UAAM,yBAAyB,OAAO,UAAU;AAChD,UAAM,2BAA2B;AACjC,WAAO,UAAU,WAAW,WAAY;AACtC,UAAI,OAAO,YAAY,cAAc,gBAAgB,SAAS;AAC5D,eAAO;AAAA,MACT;AACA,aAAO,uBAAuB,KAAK,IAAI;AAAA,IACzC;AAAA,EACF,CAAC;AACH;AACA,SAAS,WAAW;AAUlB,QAAMA,UAAS;AACf,QAAM,iBAAiBA,QAAO,WAAW,yBAAyB,CAAC,MAAM;AACzE,MAAIA,QAAO,MAAM,MAAM,kBAAkB,OAAOA,QAAO,MAAM,EAAE,eAAe,aAAa;AACzF,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,EAAAA,QAAO,MAAM,MAAM,SAAS;AAC5B,SAAOA,QAAO,MAAM;AACtB;AAOA,IAAM,iCAAiC;AAAA,EACrC,MAAM;AACR;AACA,IAAM,uBAAuB,CAAC;AAC9B,IAAM,gBAAgB,CAAC;AACvB,IAAM,yBAAyB,IAAI,OAAO,MAAM,qBAAqB,qBAAqB;AAC1F,IAAM,+BAA+B,WAAW,oBAAoB;AACpE,SAAS,kBAAkB,WAAW,mBAAmB;AACvD,QAAM,kBAAkB,oBAAoB,kBAAkB,SAAS,IAAI,aAAa;AACxF,QAAM,iBAAiB,oBAAoB,kBAAkB,SAAS,IAAI,aAAa;AACvF,QAAM,SAAS,qBAAqB;AACpC,QAAM,gBAAgB,qBAAqB;AAC3C,uBAAqB,SAAS,IAAI,CAAC;AACnC,uBAAqB,SAAS,EAAE,SAAS,IAAI;AAC7C,uBAAqB,SAAS,EAAE,QAAQ,IAAI;AAC9C;AACA,SAAS,iBAAiBK,UAAS,KAAK,MAAM,cAAc;AAC1D,QAAM,qBAAqB,gBAAgB,aAAa,OAAO;AAC/D,QAAM,wBAAwB,gBAAgB,aAAa,MAAM;AACjE,QAAM,2BAA2B,gBAAgB,aAAa,aAAa;AAC3E,QAAM,sCAAsC,gBAAgB,aAAa,SAAS;AAClF,QAAM,6BAA6B,WAAW,kBAAkB;AAChE,QAAM,4BAA4B,MAAM,qBAAqB;AAC7D,QAAM,yBAAyB;AAC/B,QAAM,gCAAgC,MAAM,yBAAyB;AACrE,QAAM,aAAa,SAAU,MAAM,QAAQ,OAAO;AAGhD,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,UAAM,WAAW,KAAK;AACtB,QAAI,OAAO,aAAa,YAAY,SAAS,aAAa;AAExD,WAAK,WAAW,CAAAC,WAAS,SAAS,YAAYA,MAAK;AACnD,WAAK,mBAAmB;AAAA,IAC1B;AAKA,QAAI;AACJ,QAAI;AACF,WAAK,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC;AAAA,IACnC,SAAS,KAAK;AACZ,cAAQ;AAAA,IACV;AACA,UAAM,UAAU,KAAK;AACrB,QAAI,WAAW,OAAO,YAAY,YAAY,QAAQ,MAAM;AAI1D,YAAMC,YAAW,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACtE,aAAO,qBAAqB,EAAE,KAAK,QAAQ,MAAM,MAAMA,WAAU,OAAO;AAAA,IAC1E;AACA,WAAO;AAAA,EACT;AACA,WAAS,eAAe,SAAS,OAAO,WAAW;AAGjD,YAAQ,SAASF,SAAQ;AACzB,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAGA,UAAM,SAAS,WAAW,MAAM,UAAUA;AAC1C,UAAM,QAAQ,OAAO,qBAAqB,MAAM,IAAI,EAAE,YAAY,WAAW,SAAS,CAAC;AACvF,QAAI,OAAO;AACT,YAAM,SAAS,CAAC;AAGhB,UAAI,MAAM,WAAW,GAAG;AACtB,cAAM,MAAM,WAAW,MAAM,CAAC,GAAG,QAAQ,KAAK;AAC9C,eAAO,OAAO,KAAK,GAAG;AAAA,MACxB,OAAO;AAIL,cAAM,YAAY,MAAM,MAAM;AAC9B,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAI,SAAS,MAAM,4BAA4B,MAAM,MAAM;AACzD;AAAA,UACF;AACA,gBAAM,MAAM,WAAW,UAAU,CAAC,GAAG,QAAQ,KAAK;AAClD,iBAAO,OAAO,KAAK,GAAG;AAAA,QACxB;AAAA,MACF;AAGA,UAAI,OAAO,WAAW,GAAG;AACvB,cAAM,OAAO,CAAC;AAAA,MAChB,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAM,MAAM,OAAO,CAAC;AACpB,cAAI,wBAAwB,MAAM;AAChC,kBAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,0BAA0B,SAAU,OAAO;AAC/C,WAAO,eAAe,MAAM,OAAO,KAAK;AAAA,EAC1C;AAEA,QAAM,iCAAiC,SAAU,OAAO;AACtD,WAAO,eAAe,MAAM,OAAO,IAAI;AAAA,EACzC;AACA,WAAS,wBAAwB,KAAKG,eAAc;AAClD,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,QAAI,oBAAoB;AACxB,QAAIA,iBAAgBA,cAAa,SAAS,QAAW;AACnD,0BAAoBA,cAAa;AAAA,IACnC;AACA,UAAM,kBAAkBA,iBAAgBA,cAAa;AACrD,QAAI,iBAAiB;AACrB,QAAIA,iBAAgBA,cAAa,WAAW,QAAW;AACrD,uBAAiBA,cAAa;AAAA,IAChC;AACA,QAAI,eAAe;AACnB,QAAIA,iBAAgBA,cAAa,OAAO,QAAW;AACjD,qBAAeA,cAAa;AAAA,IAC9B;AACA,QAAI,QAAQ;AACZ,WAAO,SAAS,CAAC,MAAM,eAAe,kBAAkB,GAAG;AACzD,cAAQ,qBAAqB,KAAK;AAAA,IACpC;AACA,QAAI,CAAC,SAAS,IAAI,kBAAkB,GAAG;AAErC,cAAQ;AAAA,IACV;AACA,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,QAAI,MAAM,0BAA0B,GAAG;AACrC,aAAO;AAAA,IACT;AACA,UAAM,oBAAoBA,iBAAgBA,cAAa;AASvD,UAAM,WAAW,CAAC;AAClB,UAAM,yBAAyB,MAAM,0BAA0B,IAAI,MAAM,kBAAkB;AAC3F,UAAM,4BAA4B,MAAM,WAAW,qBAAqB,CAAC,IAAI,MAAM,qBAAqB;AACxG,UAAM,kBAAkB,MAAM,WAAW,wBAAwB,CAAC,IAAI,MAAM,wBAAwB;AACpG,UAAM,2BAA2B,MAAM,WAAW,mCAAmC,CAAC,IAAI,MAAM,mCAAmC;AACnI,QAAI;AACJ,QAAIA,iBAAgBA,cAAa,SAAS;AACxC,mCAA6B,MAAM,WAAWA,cAAa,OAAO,CAAC,IAAI,MAAMA,cAAa,OAAO;AAAA,IACnG;AAKA,aAAS,0BAA0B,SAAS,SAAS;AACnD,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AACA,UAAI,OAAO,YAAY,WAAW;AAChC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AAAA,MACF;AACA,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,MACF;AACA,UAAI,OAAO,YAAY,YAAY,QAAQ,YAAY,OAAO;AAC5D,eAAO,iCACF,UADE;AAAA,UAEL,SAAS;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,UAAM,uBAAuB,SAAU,MAAM;AAG3C,UAAI,SAAS,YAAY;AACvB;AAAA,MACF;AACA,aAAO,uBAAuB,KAAK,SAAS,QAAQ,SAAS,WAAW,SAAS,UAAU,iCAAiC,yBAAyB,SAAS,OAAO;AAAA,IACvK;AAOA,UAAM,qBAAqB,SAAU,MAAM;AAIzC,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,mBAAmB,qBAAqB,KAAK,SAAS;AAC5D,YAAI;AACJ,YAAI,kBAAkB;AACpB,4BAAkB,iBAAiB,KAAK,UAAU,WAAW,SAAS;AAAA,QACxE;AACA,cAAM,gBAAgB,mBAAmB,KAAK,OAAO,eAAe;AACpE,YAAI,eAAe;AACjB,mBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,kBAAM,eAAe,cAAc,CAAC;AACpC,gBAAI,iBAAiB,MAAM;AACzB,4BAAc,OAAO,GAAG,CAAC;AAEzB,mBAAK,YAAY;AACjB,kBAAI,KAAK,qBAAqB;AAC5B,qBAAK,oBAAoB;AACzB,qBAAK,sBAAsB;AAAA,cAC7B;AACA,kBAAI,cAAc,WAAW,GAAG;AAG9B,qBAAK,aAAa;AAClB,qBAAK,OAAO,eAAe,IAAI;AAAA,cACjC;AACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAIA,UAAI,CAAC,KAAK,YAAY;AACpB;AAAA,MACF;AACA,aAAO,0BAA0B,KAAK,KAAK,QAAQ,KAAK,WAAW,KAAK,UAAU,iCAAiC,yBAAyB,KAAK,OAAO;AAAA,IAC1J;AACA,UAAM,0BAA0B,SAAU,MAAM;AAC9C,aAAO,uBAAuB,KAAK,SAAS,QAAQ,SAAS,WAAW,KAAK,QAAQ,SAAS,OAAO;AAAA,IACvG;AACA,UAAM,wBAAwB,SAAU,MAAM;AAC5C,aAAO,2BAA2B,KAAK,SAAS,QAAQ,SAAS,WAAW,KAAK,QAAQ,SAAS,OAAO;AAAA,IAC3G;AACA,UAAM,wBAAwB,SAAU,MAAM;AAC5C,aAAO,0BAA0B,KAAK,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,OAAO;AAAA,IAC9F;AACA,UAAM,iBAAiB,oBAAoB,uBAAuB;AAClE,UAAM,eAAe,oBAAoB,qBAAqB;AAC9D,UAAM,gCAAgC,SAAU,MAAM,UAAU;AAC9D,YAAM,iBAAiB,OAAO;AAC9B,aAAO,mBAAmB,cAAc,KAAK,aAAa,YAAY,mBAAmB,YAAY,KAAK,qBAAqB;AAAA,IACjI;AACA,UAAM,UAAUA,eAAc,QAAQ;AACtC,UAAM,kBAAkB,KAAK,WAAW,kBAAkB,CAAC;AAC3D,UAAM,gBAAgBH,SAAQ,WAAW,gBAAgB,CAAC;AAC1D,aAAS,yBAAyB,SAAS;AACzC,UAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AAInD,cAAM,aAAa,mBACd;AAWL,YAAI,QAAQ,QAAQ;AAClB,qBAAW,SAAS,QAAQ;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,SAAU,gBAAgB,WAAW,kBAAkB,gBAAgBI,gBAAe,OAAO,UAAU,OAAO;AACpI,aAAO,WAAY;AACjB,cAAM,SAAS,QAAQJ;AACvB,YAAI,YAAY,UAAU,CAAC;AAC3B,YAAIG,iBAAgBA,cAAa,mBAAmB;AAClD,sBAAYA,cAAa,kBAAkB,SAAS;AAAA,QACtD;AACA,YAAI,WAAW,UAAU,CAAC;AAC1B,YAAI,CAAC,UAAU;AACb,iBAAO,eAAe,MAAM,MAAM,SAAS;AAAA,QAC7C;AACA,YAAI,UAAU,cAAc,qBAAqB;AAE/C,iBAAO,eAAe,MAAM,MAAM,SAAS;AAAA,QAC7C;AAGA,YAAI,wBAAwB;AAC5B,YAAI,OAAO,aAAa,YAAY;AAIlC,cAAI,CAAC,SAAS,aAAa;AACzB,mBAAO,eAAe,MAAM,MAAM,SAAS;AAAA,UAC7C;AACA,kCAAwB;AAAA,QAC1B;AACA,YAAI,mBAAmB,CAAC,gBAAgB,gBAAgB,UAAU,QAAQ,SAAS,GAAG;AACpF;AAAA,QACF;AACA,cAAM,UAAU,CAAC,CAAC,iBAAiB,cAAc,QAAQ,SAAS,MAAM;AACxE,cAAM,UAAU,yBAAyB,0BAA0B,UAAU,CAAC,GAAG,OAAO,CAAC;AACzF,cAAM,SAAS,SAAS;AACxB,YAAI,QAAQ,SAAS;AAEnB;AAAA,QACF;AACA,YAAI,iBAAiB;AAEnB,mBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,gBAAI,cAAc,gBAAgB,CAAC,GAAG;AACpC,kBAAI,SAAS;AACX,uBAAO,eAAe,KAAK,QAAQ,WAAW,UAAU,OAAO;AAAA,cACjE,OAAO;AACL,uBAAO,eAAe,MAAM,MAAM,SAAS;AAAA,cAC7C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,cAAM,UAAU,CAAC,UAAU,QAAQ,OAAO,YAAY,YAAY,OAAO,QAAQ;AACjF,cAAM,OAAO,WAAW,OAAO,YAAY,WAAW,QAAQ,OAAO;AACrE,cAAM,OAAO,KAAK;AAClB,YAAI,mBAAmB,qBAAqB,SAAS;AACrD,YAAI,CAAC,kBAAkB;AACrB,4BAAkB,WAAW,iBAAiB;AAC9C,6BAAmB,qBAAqB,SAAS;AAAA,QACnD;AACA,cAAM,kBAAkB,iBAAiB,UAAU,WAAW,SAAS;AACvE,YAAI,gBAAgB,OAAO,eAAe;AAC1C,YAAI,aAAa;AACjB,YAAI,eAAe;AAEjB,uBAAa;AACb,cAAI,gBAAgB;AAClB,qBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,kBAAI,QAAQ,cAAc,CAAC,GAAG,QAAQ,GAAG;AAEvC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,0BAAgB,OAAO,eAAe,IAAI,CAAC;AAAA,QAC7C;AACA,YAAI;AACJ,cAAM,kBAAkB,OAAO,YAAY,MAAM;AACjD,cAAM,eAAe,cAAc,eAAe;AAClD,YAAI,cAAc;AAChB,mBAAS,aAAa,SAAS;AAAA,QACjC;AACA,YAAI,CAAC,QAAQ;AACX,mBAAS,kBAAkB,aAAa,oBAAoB,kBAAkB,SAAS,IAAI;AAAA,QAC7F;AAMA,iBAAS,UAAU;AACnB,YAAI,MAAM;AAIR,mBAAS,QAAQ,OAAO;AAAA,QAC1B;AACA,iBAAS,SAAS;AAClB,iBAAS,UAAU;AACnB,iBAAS,YAAY;AACrB,iBAAS,aAAa;AACtB,cAAM,OAAO,oBAAoB,iCAAiC;AAElE,YAAI,MAAM;AACR,eAAK,WAAW;AAAA,QAClB;AACA,YAAI,QAAQ;AAIV,mBAAS,QAAQ,SAAS;AAAA,QAC5B;AAKA,cAAM,OAAO,KAAK,kBAAkB,QAAQ,UAAU,MAAM,kBAAkB,cAAc;AAC5F,YAAI,QAAQ;AAEV,mBAAS,QAAQ,SAAS;AAI1B,gBAAM,UAAU,MAAM,KAAK,KAAK,WAAW,IAAI;AAC/C,yBAAe,KAAK,QAAQ,SAAS,SAAS;AAAA,YAC5C,MAAM;AAAA,UACR,CAAC;AAKD,eAAK,sBAAsB,MAAM,OAAO,oBAAoB,SAAS,OAAO;AAAA,QAC9E;AAGA,iBAAS,SAAS;AAElB,YAAI,MAAM;AACR,eAAK,WAAW;AAAA,QAClB;AAGA,YAAI,MAAM;AACR,mBAAS,QAAQ,OAAO;AAAA,QAC1B;AACA,YAAI,OAAO,KAAK,YAAY,WAAW;AAIrC,eAAK,UAAU;AAAA,QACjB;AACA,aAAK,SAAS;AACd,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,YAAI,uBAAuB;AAEzB,eAAK,mBAAmB;AAAA,QAC1B;AACA,YAAI,CAAC,SAAS;AACZ,wBAAc,KAAK,IAAI;AAAA,QACzB,OAAO;AACL,wBAAc,QAAQ,IAAI;AAAA,QAC5B;AACA,YAAIC,eAAc;AAChB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,UAAM,kBAAkB,IAAI,gBAAgB,wBAAwB,2BAA2B,gBAAgB,cAAc,YAAY;AACzI,QAAI,4BAA4B;AAC9B,YAAM,sBAAsB,IAAI,gBAAgB,4BAA4B,+BAA+B,uBAAuB,cAAc,cAAc,IAAI;AAAA,IACpK;AACA,UAAM,qBAAqB,IAAI,WAAY;AACzC,YAAM,SAAS,QAAQJ;AACvB,UAAI,YAAY,UAAU,CAAC;AAC3B,UAAIG,iBAAgBA,cAAa,mBAAmB;AAClD,oBAAYA,cAAa,kBAAkB,SAAS;AAAA,MACtD;AACA,YAAM,UAAU,UAAU,CAAC;AAC3B,YAAM,UAAU,CAAC,UAAU,QAAQ,OAAO,YAAY,YAAY,OAAO,QAAQ;AACjF,YAAM,WAAW,UAAU,CAAC;AAC5B,UAAI,CAAC,UAAU;AACb,eAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,MACxD;AACA,UAAI,mBAAmB,CAAC,gBAAgB,2BAA2B,UAAU,QAAQ,SAAS,GAAG;AAC/F;AAAA,MACF;AACA,YAAM,mBAAmB,qBAAqB,SAAS;AACvD,UAAI;AACJ,UAAI,kBAAkB;AACpB,0BAAkB,iBAAiB,UAAU,WAAW,SAAS;AAAA,MACnE;AACA,YAAM,gBAAgB,mBAAmB,OAAO,eAAe;AAK/D,UAAI,eAAe;AACjB,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,gBAAM,eAAe,cAAc,CAAC;AACpC,cAAI,QAAQ,cAAc,QAAQ,GAAG;AACnC,0BAAc,OAAO,GAAG,CAAC;AAEzB,yBAAa,YAAY;AACzB,gBAAI,cAAc,WAAW,GAAG;AAG9B,2BAAa,aAAa;AAC1B,qBAAO,eAAe,IAAI;AAM1B,kBAAI,CAAC,WAAW,OAAO,cAAc,UAAU;AAC7C,sBAAM,mBAAmB,qBAAqB,gBAAgB;AAC9D,uBAAO,gBAAgB,IAAI;AAAA,cAC7B;AAAA,YACF;AAMA,yBAAa,KAAK,WAAW,YAAY;AACzC,gBAAI,cAAc;AAChB,qBAAO;AAAA,YACT;AACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAOA,aAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,IACxD;AACA,UAAM,wBAAwB,IAAI,WAAY;AAC5C,YAAM,SAAS,QAAQH;AACvB,UAAI,YAAY,UAAU,CAAC;AAC3B,UAAIG,iBAAgBA,cAAa,mBAAmB;AAClD,oBAAYA,cAAa,kBAAkB,SAAS;AAAA,MACtD;AACA,YAAM,YAAY,CAAC;AACnB,YAAM,QAAQ,eAAe,QAAQ,oBAAoB,kBAAkB,SAAS,IAAI,SAAS;AACjG,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,OAAO,MAAM,CAAC;AACpB,YAAI,WAAW,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACpE,kBAAU,KAAK,QAAQ;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AACA,UAAM,mCAAmC,IAAI,WAAY;AACvD,YAAM,SAAS,QAAQH;AACvB,UAAI,YAAY,UAAU,CAAC;AAC3B,UAAI,CAAC,WAAW;AACd,cAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAM,OAAO,KAAK,CAAC;AACnB,gBAAM,QAAQ,uBAAuB,KAAK,IAAI;AAC9C,cAAI,UAAU,SAAS,MAAM,CAAC;AAK9B,cAAI,WAAW,YAAY,kBAAkB;AAC3C,iBAAK,mCAAmC,EAAE,KAAK,MAAM,OAAO;AAAA,UAC9D;AAAA,QACF;AAEA,aAAK,mCAAmC,EAAE,KAAK,MAAM,gBAAgB;AAAA,MACvE,OAAO;AACL,YAAIG,iBAAgBA,cAAa,mBAAmB;AAClD,sBAAYA,cAAa,kBAAkB,SAAS;AAAA,QACtD;AACA,cAAM,mBAAmB,qBAAqB,SAAS;AACvD,YAAI,kBAAkB;AACpB,gBAAM,kBAAkB,iBAAiB,SAAS;AAClD,gBAAM,yBAAyB,iBAAiB,QAAQ;AACxD,gBAAM,QAAQ,OAAO,eAAe;AACpC,gBAAM,eAAe,OAAO,sBAAsB;AAClD,cAAI,OAAO;AACT,kBAAM,cAAc,MAAM,MAAM;AAChC,qBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,oBAAM,OAAO,YAAY,CAAC;AAC1B,kBAAI,WAAW,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACpE,mBAAK,qBAAqB,EAAE,KAAK,MAAM,WAAW,UAAU,KAAK,OAAO;AAAA,YAC1E;AAAA,UACF;AACA,cAAI,cAAc;AAChB,kBAAM,cAAc,aAAa,MAAM;AACvC,qBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,oBAAM,OAAO,YAAY,CAAC;AAC1B,kBAAI,WAAW,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACpE,mBAAK,qBAAqB,EAAE,KAAK,MAAM,WAAW,UAAU,KAAK,OAAO;AAAA,YAC1E;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,cAAc;AAChB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,0BAAsB,MAAM,kBAAkB,GAAG,sBAAsB;AACvE,0BAAsB,MAAM,qBAAqB,GAAG,yBAAyB;AAC7E,QAAI,0BAA0B;AAC5B,4BAAsB,MAAM,mCAAmC,GAAG,wBAAwB;AAAA,IAC5F;AACA,QAAI,iBAAiB;AACnB,4BAAsB,MAAM,wBAAwB,GAAG,eAAe;AAAA,IACxE;AACA,WAAO;AAAA,EACT;AACA,MAAI,UAAU,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAQ,CAAC,IAAI,wBAAwB,KAAK,CAAC,GAAG,YAAY;AAAA,EAC5D;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,WAAW;AACzC,MAAI,CAAC,WAAW;AACd,UAAM,aAAa,CAAC;AACpB,aAAS,QAAQ,QAAQ;AACvB,YAAM,QAAQ,uBAAuB,KAAK,IAAI;AAC9C,UAAI,UAAU,SAAS,MAAM,CAAC;AAC9B,UAAI,YAAY,CAAC,aAAa,YAAY,YAAY;AACpD,cAAM,QAAQ,OAAO,IAAI;AACzB,YAAI,OAAO;AACT,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,uBAAW,KAAK,MAAM,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,qBAAqB,SAAS;AACpD,MAAI,CAAC,iBAAiB;AACpB,sBAAkB,SAAS;AAC3B,sBAAkB,qBAAqB,SAAS;AAAA,EAClD;AACA,QAAM,oBAAoB,OAAO,gBAAgB,SAAS,CAAC;AAC3D,QAAM,mBAAmB,OAAO,gBAAgB,QAAQ,CAAC;AACzD,MAAI,CAAC,mBAAmB;AACtB,WAAO,mBAAmB,iBAAiB,MAAM,IAAI,CAAC;AAAA,EACxD,OAAO;AACL,WAAO,mBAAmB,kBAAkB,OAAO,gBAAgB,IAAI,kBAAkB,MAAM;AAAA,EACjG;AACF;AAMA,SAAS,oBAAoBR,SAAQ,KAAK;AACxC,MAAI,YAAYA,SAAQ,kBAAkB,cAAY;AACpD,WAAO,SAAUF,OAAM,MAAM;AAC3B,WAAK,QAAQ,kBAAkB,kBAAkB,KAAK,CAAC,CAAC;AAAA,IAC1D;AAAA,EACF,CAAC;AACH;AAMA,IAAM,aAAa,WAAW,UAAU;AACxC,SAAS,WAAWY,SAAQ,SAAS,YAAY,YAAY;AAC3D,MAAI,YAAY;AAChB,MAAI,cAAc;AAClB,aAAW;AACX,gBAAc;AACd,QAAM,kBAAkB,CAAC;AACzB,WAAS,aAAa,MAAM;AAC1B,UAAM,OAAO,KAAK;AAClB,SAAK,KAAK,CAAC,IAAI,WAAY;AACzB,aAAO,KAAK,OAAO,MAAM,MAAM,SAAS;AAAA,IAC1C;AACA,UAAM,aAAa,UAAU,MAAMA,SAAQ,KAAK,IAAI;AAIpD,QAAI,SAAS,UAAU,GAAG;AACxB,WAAK,WAAW;AAAA,IAClB,OAAO;AACL,WAAK,SAAS;AAEd,WAAK,gBAAgB,WAAW,WAAW,OAAO;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AACA,WAAS,UAAU,MAAM;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,WAAO,YAAY,KAAKA,SAAQ,UAAU,QAAQ;AAAA,EACpD;AACA,cAAY,YAAYA,SAAQ,SAAS,cAAY,SAAUZ,OAAM,MAAM;AACzE,QAAI,WAAW,KAAK,CAAC,CAAC,GAAG;AACvB,YAAM,UAAU;AAAA,QACd,eAAe;AAAA,QACf,YAAY,eAAe;AAAA,QAC3B,OAAO,eAAe,aAAa,eAAe,aAAa,KAAK,CAAC,KAAK,IAAI;AAAA,QAC9E;AAAA,MACF;AACA,YAAM,WAAW,KAAK,CAAC;AACvB,WAAK,CAAC,IAAI,SAAS,QAAQ;AACzB,YAAI;AACF,iBAAO,SAAS,MAAM,MAAM,SAAS;AAAA,QACvC,UAAE;AAQA,gBAAM;AAAA,YACJ,QAAAa;AAAA,YACA,UAAAC;AAAA,YACA,YAAAC;AAAA,YACA,eAAAC;AAAA,UACF,IAAI;AACJ,cAAI,CAACD,eAAc,CAACC,gBAAe;AACjC,gBAAIF,WAAU;AAGZ,qBAAO,gBAAgBA,SAAQ;AAAA,YACjC,WAAWD,SAAQ;AAGjB,cAAAA,QAAO,UAAU,IAAI;AAAA,YACvB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO,iCAAiC,SAAS,KAAK,CAAC,GAAG,SAAS,cAAc,SAAS;AAChG,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AAEA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,UAAI,UAAU;AAGZ,wBAAgB,QAAQ,IAAI;AAAA,MAC9B,WAAW,QAAQ;AAGjB,eAAO,UAAU,IAAI;AACrB,YAAI,iBAAiB,CAAC,YAAY;AAChC,gBAAM,kBAAkB,OAAO;AAC/B,iBAAO,UAAU,WAAY;AAC3B,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI;AACJ,gBAAI,UAAU,gBAAgB;AAC5B,mBAAK,SAAS;AACd,mBAAK,iBAAiB,MAAM,CAAC;AAAA,YAC/B,WAAW,UAAU,WAAW;AAC9B,mBAAK,SAAS;AAAA,YAChB;AACA,mBAAO,gBAAgB,KAAK,IAAI;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AACA,aAAO,UAAU,YAAY;AAAA,IAC/B,OAAO;AAEL,aAAO,SAAS,MAAMD,SAAQ,IAAI;AAAA,IACpC;AAAA,EACF,CAAC;AACD,gBAAc,YAAYA,SAAQ,YAAY,cAAY,SAAUZ,OAAM,MAAM;AAC9E,UAAM,KAAK,KAAK,CAAC;AACjB,QAAI;AACJ,QAAI,SAAS,EAAE,GAAG;AAEhB,aAAO,gBAAgB,EAAE;AACzB,aAAO,gBAAgB,EAAE;AAAA,IAC3B,OAAO;AAEL,aAAO,KAAK,UAAU;AACtB,UAAI,MAAM;AACR,WAAG,UAAU,IAAI;AAAA,MACnB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,MAAM,MAAM;AACd,UAAI,KAAK,UAAU;AAEjB,aAAK,KAAK,WAAW,IAAI;AAAA,MAC3B;AAAA,IACF,OAAO;AAEL,eAAS,MAAMY,SAAQ,IAAI;AAAA,IAC7B;AAAA,EACF,CAAC;AACH;AACA,SAAS,YAAYX,OAAM;AACzB,EAAAA,MAAK,aAAa,gBAAgB,CAACC,SAAQD,OAAM,QAAQ;AAEvD,UAAM,kBAAkB;AACxB,UAAM,sBAAsB;AAC5B,UAAM,qBAAqB;AAC3B,UAAM,yBAAyB;AAC/B,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,UAAM,SAAS;AACf,UAAM,gCAAgC,SAAU,MAAM,UAAU;AAE9D,aAAO,KAAK,aAAa,YAAY,KAAK,SAAS,aAAa;AAAA,IAClE;AACA,UAAM,oBAAoB,SAAU,WAAW;AAC7C,UAAI,OAAO,cAAc,UAAU;AACjC,eAAO;AAAA,MACT;AACA,UAAI,CAAC,WAAW;AACd,eAAO;AAAA,MACT;AACA,aAAO,UAAU,SAAS,EAAE,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,GAAG;AAAA,IAChE;AACA,aAAS,yBAAyB,KAAK;AACrC,YAAM,SAAS,iBAAiBC,SAAQ,KAAK,CAAC,GAAG,GAAG;AAAA,QAClD,MAAM;AAAA,QACN,KAAK;AAAA,QACL,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,IAAI;AAAA,QACJ,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AACD,UAAI,UAAU,OAAO,CAAC,GAAG;AACvB,YAAI,KAAK,IAAI,IAAI,eAAe;AAChC,YAAI,MAAM,IAAI,IAAI,kBAAkB;AAAA,MACtC;AAAA,IACF;AAEA,QAAI;AACJ,QAAI;AACF,eAAS,UAAQ,QAAQ;AAAA,IAC3B,SAAS,KAAK;AAAA,IAAC;AACf,QAAI,UAAU,OAAO,cAAc;AACjC,+BAAyB,OAAO,aAAa,SAAS;AAAA,IACxD;AAAA,EACF,CAAC;AACH;AACA,SAAS,QAAQD,OAAM;AACrB,EAAAA,MAAK,aAAa,MAAM,CAACC,SAAQD,OAAM,QAAQ;AAC7C,QAAI;AACJ,QAAI;AACF,WAAK,UAAQ,IAAI;AAAA,IACnB,SAAS,KAAK;AAAA,IAAC;AACf,QAAI,CAAC,GAAI;AAGT,UAAM,6BAA6B,CAAC,UAAU,cAAc,SAAS,SAAS,SAAS,UAAU,UAAU,UAAU,aAAa,SAAS,SAAS,aAAa,WAAW,UAAU,UAAU,WAAW,QAAQ,SAAS,SAAS,WAAW,QAAQ,WAAW,QAAQ,WAAW,YAAY,YAAY,YAAY,UAAU,SAAS,QAAQ,WAAW,YAAY,UAAU,UAAU,SAAS,aAAa,QAAQ;AAC9Z,+BAA2B,OAAO,UAAQ,CAAC,CAAC,GAAG,IAAI,KAAK,OAAO,GAAG,IAAI,MAAM,UAAU,EAAE,QAAQ,UAAQ;AACtG,qBAAe,IAAI,MAAM,CAACD,OAAM,SAAS;AACvC,eAAO;AAAA,UACL,MAAM,QAAQ;AAAA,UACd;AAAA,UACA,OAAO,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI;AAAA,UAC3C,QAAQA;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,UAAM,2BAA2B,GAAG,WAAW,IAAI,OAAO,kBAAkB,CAAC;AAG7E,QAAI,0BAA0B,QAAQ;AACpC,SAAG,SAAS,SAAS,yBAAyB;AAC9C,qBAAe,GAAG,UAAU,UAAU,CAACA,OAAM,UAAU;AAAA,QACrD;AAAA,QACA,QAAQA;AAAA,QACR,OAAO,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI;AAAA,QAC3C,MAAM;AAAA,MACR,EAAE;AAAA,IACJ;AAAA,EACF,CAAC;AACH;AACA,SAAS,cAAcC,OAAM;AAC3B,EAAAA,MAAK,aAAa,aAAa,CAACC,SAAQD,OAAM,QAAQ;AACpD,QAAI,oBAAoB;AACxB,QAAI,cAAc;AAClB,QAAI,gBAAgB;AACpB,QAAI,iBAAiB;AACrB,kCAA8B,IAAI;AAAA,EACpC,CAAC;AACH;AACA,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,SAAS,UAAUA,OAAM;AACvB,gBAAcA,KAAI;AAClB,cAAYA,KAAI;AAChB,UAAQA,KAAI;AACZ,EAAAA,MAAK,aAAa,eAAe,CAACC,SAAQD,UAAS;AAEjD,QAAI,4BAA4B;AAChC,QAAI;AACF,YAAM,SAAS,UAAQ,QAAQ;AAC/B,UAAI,2BAA2BC,QAAO,eAAe,OAAO;AAC5D,UAAI,CAAC,4BAA4B,CAAC,OAAO;AAMvC,cAAM,mBAAmB,OAAO;AAChC,eAAO,aAAa,WAAY;AAC9B,sCAA4B;AAC5B,iBAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,QAC/C;AACA,cAAM,gBAAgBA,QAAO,WAAW,MAAM;AAAA,QAAC,GAAG,GAAG;AACrD,qBAAa,aAAa;AAC1B,eAAO,aAAa;AAAA,MACtB;AACA,iBAAW,QAAQ,KAAK,OAAO,SAAS;AACxC,iBAAW,QAAQ,KAAK,OAAO,UAAU;AACzC,iBAAW,QAAQ,KAAK,OAAO,WAAW;AAAA,IAC5C,SAAS,OAAO;AAAA,IAGhB;AACA,QAAI,OAAO;AAIT;AAAA,IACF;AACA,QAAI,CAAC,2BAA2B;AAI9B,iBAAWA,SAAQ,KAAK,OAAO,SAAS;AACxC,iBAAWA,SAAQ,KAAK,OAAO,UAAU;AACzC,iBAAWA,SAAQ,KAAK,OAAO,WAAW;AAAA,IAC5C,OAAO;AAML,MAAAA,QAAOD,MAAK,WAAW,YAAY,CAAC,IAAIC,QAAO;AAC/C,MAAAA,QAAOD,MAAK,WAAW,aAAa,CAAC,IAAIC,QAAO;AAChD,MAAAA,QAAOD,MAAK,WAAW,cAAc,CAAC,IAAIC,QAAO;AAAA,IACnD;AAAA,EACF,CAAC;AAED,EAAAD,MAAK,aAAa,YAAY,MAAM;AAElC,mBAAe,SAAS,YAAY,CAACD,OAAM,SAAS;AAClD,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,OAAO,KAAK,SAAS,KAAK,OAAO,KAAK,CAAC,MAAM,aAAa,IAAI;AAAA,QAC9D,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,EAAAC,MAAK,aAAa,mCAAmC,CAACC,SAAQD,OAAM,QAAQ;AAC1E,IAAAA,MAAK,IAAI,OAAO,kCAAkC,CAAC,IAAI,mCAAmC,oBAAoB;AAC9G,IAAAA,MAAK,IAAI,OAAO,yBAAyB,CAAC,IAAI,mCAAmC,kBAAkB;AAEnG,aAAS,mCAAmC,SAAS;AACnD,aAAO,SAAU,GAAG;AAClB,cAAM,aAAa,eAAe,SAAS,OAAO;AAClD,mBAAW,QAAQ,eAAa;AAG9B,cAAI,YAAY,sBAAsB;AACpC,sBAAU,OAAO,EAAE,WAAW,EAAE,OAAO;AAAA,UACzC,WAAW,YAAY,oBAAoB;AACzC,sBAAU,OAAO,EAAE,OAAO;AAAA,UAC5B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AAED,EAAAA,MAAK,aAAa,UAAU,MAAM;AAChC,QAAI;AACJ,QAAI;AACF,eAAS,UAAQ,QAAQ;AAAA,IAC3B,SAAS,KAAK;AAAA,IAAC;AAEf,QAAI,QAAQ;AACV,YAAM,cAAc,CAAC,eAAe,QAAQ;AAC5C,kBAAY,QAAQ,UAAQ;AAC1B,uBAAe,QAAQ,MAAM,CAACD,OAAM,SAAS;AAC3C,iBAAO;AAAA,YACL,MAAM,YAAY;AAAA,YAClB;AAAA,YACA,OAAO,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,aAAa,KAAK,SAAS,IAAI;AAAA,YAC1F,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,EAAAC,MAAK,aAAa,WAAW,CAACC,SAAQD,UAAS;AAC7C,UAAM,iBAAiB,CAAC,OAAO,OAAO,QAAQ,SAAS,QAAQ,UAAU,SAAS,WAAW,OAAO;AACpG,mBAAe,QAAQ,OAAK;AAC1B,YAAM,iBAAiB,QAAQA,MAAK,WAAW,CAAC,CAAC,IAAI,QAAQ,CAAC;AAC9D,UAAI,gBAAgB;AAClB,gBAAQ,CAAC,IAAI,WAAY;AACvB,gBAAM,OAAO,WAAW,KAAK,SAAS;AACtC,cAAIA,MAAK,YAAYA,MAAK,MAAM;AAC9B,mBAAO,eAAe,MAAM,MAAM,IAAI;AAAA,UACxC,OAAO;AACL,mBAAOA,MAAK,KAAK,IAAI,gBAAgB,MAAM,IAAI;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,EAAAA,MAAK,aAAa,kBAAkB,CAACC,SAAQD,OAAM,QAAQ;AACzD,wBAAoBC,SAAQ,GAAG;AAAA,EACjC,CAAC;AACH;AACA,SAAS,aAAa;AACpB,QAAMD,QAAO,SAAS;AACtB,YAAUA,KAAI;AACd,eAAaA,KAAI;AACjB,gBAAcA,KAAI;AAClB,SAAOA;AACT;AACA,WAAW;", "names": ["self", "Zone", "global", "ObjectGetOwnPropertyDescriptor", "ObjectDefineProperty", "__symbol__", "value", "_global", "event", "delegate", "patchOptions", "<PERSON><PERSON><PERSON><PERSON>", "window", "handle", "handleId", "isPeriodic", "isRefreshable"]}