{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/sidenav.mjs"], "sourcesContent": ["import { FocusTrapFactory, FocusMonitor, InteractivityChecker } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Platform } from '@angular/cdk/platform';\nimport { CdkScrollable, ScrollDispatcher, ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, NgZone, Component, ChangeDetectionStrategy, ViewEncapsulation, Renderer2, EventEmitter, Injector, afterNextRender, Input, Output, ViewChild, ANIMATION_MODULE_TYPE, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, take, startWith, debounceTime } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"content\"];\nconst _c2 = [[[\"mat-drawer\"]], [[\"mat-drawer-content\"]], \"*\"];\nconst _c3 = [\"mat-drawer\", \"mat-drawer-content\", \"*\"];\nfunction MatDrawerContainer_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function MatDrawerContainer_Conditional_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r1._isShowingBackdrop());\n  }\n}\nfunction MatDrawerContainer_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-drawer-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c4 = [[[\"mat-sidenav\"]], [[\"mat-sidenav-content\"]], \"*\"];\nconst _c5 = [\"mat-sidenav\", \"mat-sidenav-content\", \"*\"];\nfunction MatSidenavContainer_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function MatSidenavContainer_Conditional_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r1._isShowingBackdrop());\n  }\n}\nfunction MatSidenavContainer_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-sidenav-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c6 = \".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\";\nfunction throwMatDuplicatedDrawerError(position) {\n  throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n  providedIn: 'root',\n  factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nconst MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n  return false;\n}\nclass MatDrawerContent extends CdkScrollable {\n  _platform = inject(Platform);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _container = inject(MatDrawerContainer);\n  constructor() {\n    const elementRef = inject(ElementRef);\n    const scrollDispatcher = inject(ScrollDispatcher);\n    const ngZone = inject(NgZone);\n    super(elementRef, scrollDispatcher, ngZone);\n  }\n  ngAfterContentInit() {\n    this._container._contentMarginChanges.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Determines whether the content element should be hidden from the user. */\n  _shouldBeHidden() {\n    // In some modes the content is pushed based on the width of the opened sidenavs, however on\n    // the server we can't measure the sidenav so the margin is always zero. This can cause the\n    // content to jump around when it's rendered on the server and hydrated on the client. We\n    // avoid it by hiding the content on the initial render and then showing it once the sidenav\n    // has been measured on the client.\n    if (this._platform.isBrowser) {\n      return false;\n    }\n    const {\n      start,\n      end\n    } = this._container;\n    return start != null && start.mode !== 'over' && start.opened || end != null && end.mode !== 'over' && end.opened;\n  }\n  static ɵfac = function MatDrawerContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDrawerContent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDrawerContent,\n    selectors: [[\"mat-drawer-content\"]],\n    hostAttrs: [1, \"mat-drawer-content\"],\n    hostVars: 6,\n    hostBindings: function MatDrawerContent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n        i0.ɵɵclassProp(\"mat-drawer-content-hidden\", ctx._shouldBeHidden());\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkScrollable,\n      useExisting: MatDrawerContent\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatDrawerContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawerContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer-content',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-drawer-content',\n        '[style.margin-left.px]': '_container._contentMargins.left',\n        '[style.margin-right.px]': '_container._contentMargins.right',\n        '[class.mat-drawer-content-hidden]': '_shouldBeHidden()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: MatDrawerContent\n      }]\n    }]\n  }], () => [], null);\n})();\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\nclass MatDrawer {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  _focusMonitor = inject(FocusMonitor);\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _renderer = inject(Renderer2);\n  _interactivityChecker = inject(InteractivityChecker);\n  _doc = inject(DOCUMENT, {\n    optional: true\n  });\n  _container = inject(MAT_DRAWER_CONTAINER, {\n    optional: true\n  });\n  _focusTrap = null;\n  _elementFocusedBeforeDrawerWasOpened = null;\n  _eventCleanups;\n  /** Whether the view of the component has been attached. */\n  _isAttached;\n  /** Anchor node used to restore the drawer to its initial position. */\n  _anchor;\n  /** The side that the drawer is attached to. */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    // Make sure we have a valid value.\n    value = value === 'end' ? 'end' : 'start';\n    if (value !== this._position) {\n      // Static inputs in Ivy are set before the element is in the DOM.\n      if (this._isAttached) {\n        this._updatePositionInParent(value);\n      }\n      this._position = value;\n      this.onPositionChanged.emit();\n    }\n  }\n  _position = 'start';\n  /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n  get mode() {\n    return this._mode;\n  }\n  set mode(value) {\n    this._mode = value;\n    this._updateFocusTrapState();\n    this._modeChanged.next();\n  }\n  _mode = 'over';\n  /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n  get disableClose() {\n    return this._disableClose;\n  }\n  set disableClose(value) {\n    this._disableClose = coerceBooleanProperty(value);\n  }\n  _disableClose = false;\n  /**\n   * Whether the drawer should focus the first focusable element automatically when opened.\n   * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n   * enabled, focus will be moved into the sidenav in `side` mode as well.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n   * instead.\n   */\n  get autoFocus() {\n    const value = this._autoFocus;\n    // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n    // because we don't know how the sidenav is being used, but in some cases it still makes\n    // sense to do it. The consumer can explicitly set `autoFocus`.\n    if (value == null) {\n      if (this.mode === 'side') {\n        return 'dialog';\n      } else {\n        return 'first-tabbable';\n      }\n    }\n    return value;\n  }\n  set autoFocus(value) {\n    if (value === 'true' || value === 'false' || value == null) {\n      value = coerceBooleanProperty(value);\n    }\n    this._autoFocus = value;\n  }\n  _autoFocus;\n  /**\n   * Whether the drawer is opened. We overload this because we trigger an event when it\n   * starts or end.\n   */\n  get opened() {\n    return this._opened;\n  }\n  set opened(value) {\n    this.toggle(coerceBooleanProperty(value));\n  }\n  _opened = false;\n  /** How the sidenav was opened (keypress, mouse click etc.) */\n  _openedVia;\n  /** Emits whenever the drawer has started animating. */\n  _animationStarted = new Subject();\n  /** Emits whenever the drawer is done animating. */\n  _animationEnd = new Subject();\n  /** Event emitted when the drawer open state is changed. */\n  openedChange =\n  // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n  new EventEmitter(/* isAsync */true);\n  /** Event emitted when the drawer has been opened. */\n  _openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n  /** Event emitted when the drawer has started opening. */\n  openedStart = this._animationStarted.pipe(filter(() => this.opened), mapTo(undefined));\n  /** Event emitted when the drawer has been closed. */\n  _closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n  /** Event emitted when the drawer has started closing. */\n  closedStart = this._animationStarted.pipe(filter(() => !this.opened), mapTo(undefined));\n  /** Emits when the component is destroyed. */\n  _destroyed = new Subject();\n  /** Event emitted when the drawer's position changes. */\n  // tslint:disable-next-line:no-output-on-prefix\n  onPositionChanged = new EventEmitter();\n  /** Reference to the inner element that contains all the content. */\n  _content;\n  /**\n   * An observable that emits when the drawer mode changes. This is used by the drawer container to\n   * to know when to when the mode changes so it can adapt the margins on the content.\n   */\n  _modeChanged = new Subject();\n  _injector = inject(Injector);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  constructor() {\n    this.openedChange.pipe(takeUntil(this._destroyed)).subscribe(opened => {\n      if (opened) {\n        if (this._doc) {\n          this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n        }\n        this._takeFocus();\n      } else if (this._isFocusWithinDrawer()) {\n        this._restoreFocus(this._openedVia || 'program');\n      }\n    });\n    /**\n     * Listen to `keydown` events outside the zone so that change detection is not run every\n     * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n     * and we don't have close disabled.\n     */\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._elementRef.nativeElement;\n      fromEvent(element, 'keydown').pipe(filter(event => {\n        return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n      }), takeUntil(this._destroyed)).subscribe(event => this._ngZone.run(() => {\n        this.close();\n        event.stopPropagation();\n        event.preventDefault();\n      }));\n      this._eventCleanups = [this._renderer.listen(element, 'transitionrun', this._handleTransitionEvent), this._renderer.listen(element, 'transitionend', this._handleTransitionEvent), this._renderer.listen(element, 'transitioncancel', this._handleTransitionEvent)];\n    });\n    this._animationEnd.subscribe(() => {\n      this.openedChange.emit(this._opened);\n    });\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          cleanupBlur();\n          cleanupMousedown();\n          element.removeAttribute('tabindex');\n        };\n        const cleanupBlur = this._renderer.listen(element, 'blur', callback);\n        const cleanupMousedown = this._renderer.listen(element, 'mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves focus into the drawer. Note that this works even if\n   * the focus trap is disabled in `side` mode.\n   */\n  _takeFocus() {\n    if (!this._focusTrap) {\n      return;\n    }\n    const element = this._elementRef.nativeElement;\n    // When autoFocus is not on the sidenav, if the element cannot be focused or does\n    // not exist, focus the sidenav itself so the keyboard navigation still works.\n    // We need to check that `focus` is a function due to Universal.\n    switch (this.autoFocus) {\n      case false:\n      case 'dialog':\n        return;\n      case true:\n      case 'first-tabbable':\n        afterNextRender(() => {\n          const hasMovedFocus = this._focusTrap.focusInitialElement();\n          if (!hasMovedFocus && typeof element.focus === 'function') {\n            element.focus();\n          }\n        }, {\n          injector: this._injector\n        });\n        break;\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n        break;\n      default:\n        this._focusByCssSelector(this.autoFocus);\n        break;\n    }\n  }\n  /**\n   * Restores focus to the element that was originally focused when the drawer opened.\n   * If no element was focused at that time, the focus will be restored to the drawer.\n   */\n  _restoreFocus(focusOrigin) {\n    if (this.autoFocus === 'dialog') {\n      return;\n    }\n    if (this._elementFocusedBeforeDrawerWasOpened) {\n      this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n    } else {\n      this._elementRef.nativeElement.blur();\n    }\n    this._elementFocusedBeforeDrawerWasOpened = null;\n  }\n  /** Whether focus is currently within the drawer. */\n  _isFocusWithinDrawer() {\n    const activeEl = this._doc.activeElement;\n    return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n  }\n  ngAfterViewInit() {\n    this._isAttached = true;\n    // Only update the DOM position when the sidenav is positioned at\n    // the end since we project the sidenav before the content by default.\n    if (this._position === 'end') {\n      this._updatePositionInParent('end');\n    }\n    // Needs to happen after the position is updated\n    // so the focus trap anchors are in the right place.\n    if (this._platform.isBrowser) {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n      this._updateFocusTrapState();\n    }\n  }\n  ngOnDestroy() {\n    this._eventCleanups.forEach(cleanup => cleanup());\n    this._focusTrap?.destroy();\n    this._anchor?.remove();\n    this._anchor = null;\n    this._animationStarted.complete();\n    this._animationEnd.complete();\n    this._modeChanged.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Open the drawer.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n  open(openedVia) {\n    return this.toggle(true, openedVia);\n  }\n  /** Close the drawer. */\n  close() {\n    return this.toggle(false);\n  }\n  /** Closes the drawer with context that the backdrop was clicked. */\n  _closeViaBackdropClick() {\n    // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n    // don't need to check whether focus is currently in the drawer, as clicking on the\n    // backdrop causes blurs the active element.\n    return this._setOpen(/* isOpen */false, /* restoreFocus */true, 'mouse');\n  }\n  /**\n   * Toggle this drawer.\n   * @param isOpen Whether the drawer should be open.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n  toggle(isOpen = !this.opened, openedVia) {\n    // If the focus is currently inside the drawer content and we are closing the drawer,\n    // restore the focus to the initially focused element (when the drawer opened).\n    if (isOpen && openedVia) {\n      this._openedVia = openedVia;\n    }\n    const result = this._setOpen(isOpen, /* restoreFocus */!isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n    if (!isOpen) {\n      this._openedVia = null;\n    }\n    return result;\n  }\n  /**\n   * Toggles the opened state of the drawer.\n   * @param isOpen Whether the drawer should open or close.\n   * @param restoreFocus Whether focus should be restored on close.\n   * @param focusOrigin Origin to use when restoring focus.\n   */\n  _setOpen(isOpen, restoreFocus, focusOrigin) {\n    if (isOpen === this._opened) {\n      return Promise.resolve(isOpen ? 'open' : 'close');\n    }\n    this._opened = isOpen;\n    if (this._container?._transitionsEnabled) {\n      // Note: it's importatnt to set this as early as possible,\n      // otherwise the animation can look glitchy in some cases.\n      this._setIsAnimating(true);\n    } else {\n      // Simulate the animation events if animations are disabled.\n      setTimeout(() => {\n        this._animationStarted.next();\n        this._animationEnd.next();\n      });\n    }\n    this._elementRef.nativeElement.classList.toggle('mat-drawer-opened', isOpen);\n    if (!isOpen && restoreFocus) {\n      this._restoreFocus(focusOrigin);\n    }\n    // Needed to ensure that the closing sequence fires off correctly.\n    this._changeDetectorRef.markForCheck();\n    this._updateFocusTrapState();\n    return new Promise(resolve => {\n      this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n    });\n  }\n  /** Toggles whether the drawer is currently animating. */\n  _setIsAnimating(isAnimating) {\n    this._elementRef.nativeElement.classList.toggle('mat-drawer-animating', isAnimating);\n  }\n  _getWidth() {\n    return this._elementRef.nativeElement.offsetWidth || 0;\n  }\n  /** Updates the enabled state of the focus trap. */\n  _updateFocusTrapState() {\n    if (this._focusTrap) {\n      // Trap focus only if the backdrop is enabled. Otherwise, allow end user to interact with the\n      // sidenav content.\n      this._focusTrap.enabled = !!this._container?.hasBackdrop && this.opened;\n    }\n  }\n  /**\n   * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n   * when it's in the `end` position so that it comes after the content and the visual order\n   * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n   * started off as `end` and was changed to `start`.\n   */\n  _updatePositionInParent(newPosition) {\n    // Don't move the DOM node around on the server, because it can throw off hydration.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const element = this._elementRef.nativeElement;\n    const parent = element.parentNode;\n    if (newPosition === 'end') {\n      if (!this._anchor) {\n        this._anchor = this._doc.createComment('mat-drawer-anchor');\n        parent.insertBefore(this._anchor, element);\n      }\n      parent.appendChild(element);\n    } else if (this._anchor) {\n      this._anchor.parentNode.insertBefore(element, this._anchor);\n    }\n  }\n  /** Event handler for animation events. */\n  _handleTransitionEvent = event => {\n    const element = this._elementRef.nativeElement;\n    if (event.target === element) {\n      this._ngZone.run(() => {\n        if (event.type === 'transitionrun') {\n          this._animationStarted.next(event);\n        } else {\n          // Don't toggle the animating state on `transitioncancel` since another animation should\n          // start afterwards. This prevents the drawer from blinking if an animation is interrupted.\n          if (event.type === 'transitionend') {\n            this._setIsAnimating(false);\n          }\n          this._animationEnd.next(event);\n        }\n      });\n    }\n  };\n  static ɵfac = function MatDrawer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDrawer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDrawer,\n    selectors: [[\"mat-drawer\"]],\n    viewQuery: function MatDrawer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-drawer\"],\n    hostVars: 12,\n    hostBindings: function MatDrawer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"align\", null)(\"tabIndex\", ctx.mode !== \"side\" ? \"-1\" : null);\n        i0.ɵɵstyleProp(\"visibility\", !ctx._container && !ctx.opened ? \"hidden\" : null);\n        i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\");\n      }\n    },\n    inputs: {\n      position: \"position\",\n      mode: \"mode\",\n      disableClose: \"disableClose\",\n      autoFocus: \"autoFocus\",\n      opened: \"opened\"\n    },\n    outputs: {\n      openedChange: \"openedChange\",\n      _openedStream: \"opened\",\n      openedStart: \"openedStart\",\n      _closedStream: \"closed\",\n      closedStart: \"closedStart\",\n      onPositionChanged: \"positionChanged\"\n    },\n    exportAs: [\"matDrawer\"],\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 0,\n    consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"]],\n    template: function MatDrawer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [CdkScrollable],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer',\n      exportAs: 'matDrawer',\n      host: {\n        'class': 'mat-drawer',\n        // must prevent the browser from aligning text based on value\n        '[attr.align]': 'null',\n        '[class.mat-drawer-end]': 'position === \"end\"',\n        '[class.mat-drawer-over]': 'mode === \"over\"',\n        '[class.mat-drawer-push]': 'mode === \"push\"',\n        '[class.mat-drawer-side]': 'mode === \"side\"',\n        // The styles that render the sidenav off-screen come from the drawer container. Prior to #30235\n        // this was also done by the animations module which some internal tests seem to depend on.\n        // Simulate it by toggling the `hidden` attribute instead.\n        '[style.visibility]': '(!_container && !opened) ? \"hidden\" : null',\n        // The sidenav container should not be focused on when used in side mode. See b/286459024 for\n        // reference. Updates tabIndex of drawer/container to default to null if in side mode.\n        '[attr.tabIndex]': '(mode !== \"side\") ? \"-1\" : null'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkScrollable],\n      template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\"\n    }]\n  }], () => [], {\n    position: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    disableClose: [{\n      type: Input\n    }],\n    autoFocus: [{\n      type: Input\n    }],\n    opened: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    openedStart: [{\n      type: Output\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    closedStart: [{\n      type: Output\n    }],\n    onPositionChanged: [{\n      type: Output,\n      args: ['positionChanged']\n    }],\n    _content: [{\n      type: ViewChild,\n      args: ['content']\n    }]\n  });\n})();\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\nclass MatDrawerContainer {\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _element = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _transitionsEnabled = false;\n  /** All drawers in the container. Includes drawers from inside nested containers. */\n  _allDrawers;\n  /** Drawers that belong to this container. */\n  _drawers = new QueryList();\n  _content;\n  _userContent;\n  /** The drawer child with the `start` position. */\n  get start() {\n    return this._start;\n  }\n  /** The drawer child with the `end` position. */\n  get end() {\n    return this._end;\n  }\n  /**\n   * Whether to automatically resize the container whenever\n   * the size of any of its drawers changes.\n   *\n   * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n   * the drawers on every change detection cycle. Can be configured globally via the\n   * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n   */\n  get autosize() {\n    return this._autosize;\n  }\n  set autosize(value) {\n    this._autosize = coerceBooleanProperty(value);\n  }\n  _autosize = inject(MAT_DRAWER_DEFAULT_AUTOSIZE);\n  /**\n   * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n   * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n   * mode as well.\n   */\n  get hasBackdrop() {\n    return this._drawerHasBackdrop(this._start) || this._drawerHasBackdrop(this._end);\n  }\n  set hasBackdrop(value) {\n    this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n  }\n  _backdropOverride;\n  /** Event emitted when the drawer backdrop is clicked. */\n  backdropClick = new EventEmitter();\n  /** The drawer at the start/end position, independent of direction. */\n  _start;\n  _end;\n  /**\n   * The drawer at the left/right. When direction changes, these will change as well.\n   * They're used as aliases for the above to set the left/right style properly.\n   * In LTR, _left == _start and _right == _end.\n   * In RTL, _left == _end and _right == _start.\n   */\n  _left;\n  _right;\n  /** Emits when the component is destroyed. */\n  _destroyed = new Subject();\n  /** Emits on every ngDoCheck. Used for debouncing reflows. */\n  _doCheckSubject = new Subject();\n  /**\n   * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n   * drawer is open. We use margin rather than transform even for push mode because transform breaks\n   * fixed position elements inside of the transformed element.\n   */\n  _contentMargins = {\n    left: null,\n    right: null\n  };\n  _contentMarginChanges = new Subject();\n  /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n  get scrollable() {\n    return this._userContent || this._content;\n  }\n  _injector = inject(Injector);\n  constructor() {\n    const platform = inject(Platform);\n    const viewportRuler = inject(ViewportRuler);\n    // If a `Dir` directive exists up the tree, listen direction changes\n    // and update the left/right properties to point to the proper start/end.\n    this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._validateDrawers();\n      this.updateContentMargins();\n    });\n    // Since the minimum width of the sidenav depends on the viewport width,\n    // we need to recompute the margins if the viewport changes.\n    viewportRuler.change().pipe(takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n    if (this._animationMode !== 'NoopAnimations' && platform.isBrowser) {\n      this._ngZone.runOutsideAngular(() => {\n        // Enable the animations after a delay in order to skip\n        // the initial transition if a drawer is open by default.\n        setTimeout(() => {\n          this._element.nativeElement.classList.add('mat-drawer-transition');\n          this._transitionsEnabled = true;\n        }, 200);\n      });\n    }\n  }\n  ngAfterContentInit() {\n    this._allDrawers.changes.pipe(startWith(this._allDrawers), takeUntil(this._destroyed)).subscribe(drawer => {\n      this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n      this._drawers.notifyOnChanges();\n    });\n    this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n      this._validateDrawers();\n      this._drawers.forEach(drawer => {\n        this._watchDrawerToggle(drawer);\n        this._watchDrawerPosition(drawer);\n        this._watchDrawerMode(drawer);\n      });\n      if (!this._drawers.length || this._isDrawerOpen(this._start) || this._isDrawerOpen(this._end)) {\n        this.updateContentMargins();\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n    // Avoid hitting the NgZone through the debounce timeout.\n    this._ngZone.runOutsideAngular(() => {\n      this._doCheckSubject.pipe(debounceTime(10),\n      // Arbitrary debounce time, less than a frame at 60fps\n      takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n    });\n  }\n  ngOnDestroy() {\n    this._contentMarginChanges.complete();\n    this._doCheckSubject.complete();\n    this._drawers.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Calls `open` of both start and end drawers */\n  open() {\n    this._drawers.forEach(drawer => drawer.open());\n  }\n  /** Calls `close` of both start and end drawers */\n  close() {\n    this._drawers.forEach(drawer => drawer.close());\n  }\n  /**\n   * Recalculates and updates the inline styles for the content. Note that this should be used\n   * sparingly, because it causes a reflow.\n   */\n  updateContentMargins() {\n    // 1. For drawers in `over` mode, they don't affect the content.\n    // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n    //    left margin (for left drawer) or right margin (for right the drawer).\n    // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n    //    adding to the left or right margin and simultaneously subtracting the same amount of\n    //    margin from the other side.\n    let left = 0;\n    let right = 0;\n    if (this._left && this._left.opened) {\n      if (this._left.mode == 'side') {\n        left += this._left._getWidth();\n      } else if (this._left.mode == 'push') {\n        const width = this._left._getWidth();\n        left += width;\n        right -= width;\n      }\n    }\n    if (this._right && this._right.opened) {\n      if (this._right.mode == 'side') {\n        right += this._right._getWidth();\n      } else if (this._right.mode == 'push') {\n        const width = this._right._getWidth();\n        right += width;\n        left -= width;\n      }\n    }\n    // If either `right` or `left` is zero, don't set a style to the element. This\n    // allows users to specify a custom size via CSS class in SSR scenarios where the\n    // measured widths will always be zero. Note that we reset to `null` here, rather\n    // than below, in order to ensure that the types in the `if` below are consistent.\n    left = left || null;\n    right = right || null;\n    if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n      this._contentMargins = {\n        left,\n        right\n      };\n      // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n      // to do it only when something changed, otherwise we can end up hitting the zone too often.\n      this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n    }\n  }\n  ngDoCheck() {\n    // If users opted into autosizing, do a check every change detection cycle.\n    if (this._autosize && this._isPushed()) {\n      // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n      this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n    }\n  }\n  /**\n   * Subscribes to drawer events in order to set a class on the main container element when the\n   * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n   * is properly hidden.\n   */\n  _watchDrawerToggle(drawer) {\n    drawer._animationStarted.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n      this.updateContentMargins();\n      this._changeDetectorRef.markForCheck();\n    });\n    if (drawer.mode !== 'side') {\n      drawer.openedChange.pipe(takeUntil(this._drawers.changes)).subscribe(() => this._setContainerClass(drawer.opened));\n    }\n  }\n  /**\n   * Subscribes to drawer onPositionChanged event in order to\n   * re-validate drawers when the position changes.\n   */\n  _watchDrawerPosition(drawer) {\n    // NOTE: We need to wait for the microtask queue to be empty before validating,\n    // since both drawers may be swapping positions at the same time.\n    drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n      afterNextRender({\n        read: () => this._validateDrawers()\n      }, {\n        injector: this._injector\n      });\n    });\n  }\n  /** Subscribes to changes in drawer mode so we can run change detection. */\n  _watchDrawerMode(drawer) {\n    drawer._modeChanged.pipe(takeUntil(merge(this._drawers.changes, this._destroyed))).subscribe(() => {\n      this.updateContentMargins();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n  _setContainerClass(isAdd) {\n    const classList = this._element.nativeElement.classList;\n    const className = 'mat-drawer-container-has-open';\n    if (isAdd) {\n      classList.add(className);\n    } else {\n      classList.remove(className);\n    }\n  }\n  /** Validate the state of the drawer children components. */\n  _validateDrawers() {\n    this._start = this._end = null;\n    // Ensure that we have at most one start and one end drawer.\n    this._drawers.forEach(drawer => {\n      if (drawer.position == 'end') {\n        if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('end');\n        }\n        this._end = drawer;\n      } else {\n        if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('start');\n        }\n        this._start = drawer;\n      }\n    });\n    this._right = this._left = null;\n    // Detect if we're LTR or RTL.\n    if (this._dir && this._dir.value === 'rtl') {\n      this._left = this._end;\n      this._right = this._start;\n    } else {\n      this._left = this._start;\n      this._right = this._end;\n    }\n  }\n  /** Whether the container is being pushed to the side by one of the drawers. */\n  _isPushed() {\n    return this._isDrawerOpen(this._start) && this._start.mode != 'over' || this._isDrawerOpen(this._end) && this._end.mode != 'over';\n  }\n  _onBackdropClicked() {\n    this.backdropClick.emit();\n    this._closeModalDrawersViaBackdrop();\n  }\n  _closeModalDrawersViaBackdrop() {\n    // Close all open drawers where closing is not disabled and the mode is not `side`.\n    [this._start, this._end].filter(drawer => drawer && !drawer.disableClose && this._drawerHasBackdrop(drawer)).forEach(drawer => drawer._closeViaBackdropClick());\n  }\n  _isShowingBackdrop() {\n    return this._isDrawerOpen(this._start) && this._drawerHasBackdrop(this._start) || this._isDrawerOpen(this._end) && this._drawerHasBackdrop(this._end);\n  }\n  _isDrawerOpen(drawer) {\n    return drawer != null && drawer.opened;\n  }\n  // Whether argument drawer should have a backdrop when it opens\n  _drawerHasBackdrop(drawer) {\n    if (this._backdropOverride == null) {\n      return !!drawer && drawer.mode !== 'side';\n    }\n    return this._backdropOverride;\n  }\n  static ɵfac = function MatDrawerContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDrawerContainer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDrawerContainer,\n    selectors: [[\"mat-drawer-container\"]],\n    contentQueries: function MatDrawerContainer_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatDrawerContent, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatDrawer, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n      }\n    },\n    viewQuery: function MatDrawerContainer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatDrawerContent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._userContent = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-drawer-container\"],\n    hostVars: 2,\n    hostBindings: function MatDrawerContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n      }\n    },\n    inputs: {\n      autosize: \"autosize\",\n      hasBackdrop: \"hasBackdrop\"\n    },\n    outputs: {\n      backdropClick: \"backdropClick\"\n    },\n    exportAs: [\"matDrawerContainer\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_DRAWER_CONTAINER,\n      useExisting: MatDrawerContainer\n    }])],\n    ngContentSelectors: _c3,\n    decls: 4,\n    vars: 2,\n    consts: [[1, \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n    template: function MatDrawerContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵtemplate(0, MatDrawerContainer_Conditional_0_Template, 1, 2, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵtemplate(3, MatDrawerContainer_Conditional_3_Template, 2, 0, \"mat-drawer-content\");\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.hasBackdrop ? 0 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(!ctx._content ? 3 : -1);\n      }\n    },\n    dependencies: [MatDrawerContent],\n    styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawerContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer-container',\n      exportAs: 'matDrawerContainer',\n      host: {\n        'class': 'mat-drawer-container',\n        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatDrawerContainer\n      }],\n      imports: [MatDrawerContent],\n      template: \"@if (hasBackdrop) {\\n  <div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\"\\n       [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n}\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n\\n@if (!_content) {\\n  <mat-drawer-content>\\n    <ng-content></ng-content>\\n  </mat-drawer-content>\\n}\\n\",\n      styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\"]\n    }]\n  }], () => [], {\n    _allDrawers: [{\n      type: ContentChildren,\n      args: [MatDrawer, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }],\n    _content: [{\n      type: ContentChild,\n      args: [MatDrawerContent]\n    }],\n    _userContent: [{\n      type: ViewChild,\n      args: [MatDrawerContent]\n    }],\n    autosize: [{\n      type: Input\n    }],\n    hasBackdrop: [{\n      type: Input\n    }],\n    backdropClick: [{\n      type: Output\n    }]\n  });\n})();\nclass MatSidenavContent extends MatDrawerContent {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatSidenavContent_BaseFactory;\n    return function MatSidenavContent_Factory(__ngFactoryType__) {\n      return (ɵMatSidenavContent_BaseFactory || (ɵMatSidenavContent_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenavContent)))(__ngFactoryType__ || MatSidenavContent);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSidenavContent,\n    selectors: [[\"mat-sidenav-content\"]],\n    hostAttrs: [1, \"mat-drawer-content\", \"mat-sidenav-content\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkScrollable,\n      useExisting: MatSidenavContent\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatSidenavContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav-content',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-drawer-content mat-sidenav-content'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: MatSidenavContent\n      }]\n    }]\n  }], null, null);\n})();\nclass MatSidenav extends MatDrawer {\n  /** Whether the sidenav is fixed in the viewport. */\n  get fixedInViewport() {\n    return this._fixedInViewport;\n  }\n  set fixedInViewport(value) {\n    this._fixedInViewport = coerceBooleanProperty(value);\n  }\n  _fixedInViewport = false;\n  /**\n   * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n   * mode.\n   */\n  get fixedTopGap() {\n    return this._fixedTopGap;\n  }\n  set fixedTopGap(value) {\n    this._fixedTopGap = coerceNumberProperty(value);\n  }\n  _fixedTopGap = 0;\n  /**\n   * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n   * fixed mode.\n   */\n  get fixedBottomGap() {\n    return this._fixedBottomGap;\n  }\n  set fixedBottomGap(value) {\n    this._fixedBottomGap = coerceNumberProperty(value);\n  }\n  _fixedBottomGap = 0;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatSidenav_BaseFactory;\n    return function MatSidenav_Factory(__ngFactoryType__) {\n      return (ɵMatSidenav_BaseFactory || (ɵMatSidenav_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenav)))(__ngFactoryType__ || MatSidenav);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSidenav,\n    selectors: [[\"mat-sidenav\"]],\n    hostAttrs: [1, \"mat-drawer\", \"mat-sidenav\"],\n    hostVars: 16,\n    hostBindings: function MatSidenav_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"tabIndex\", ctx.mode !== \"side\" ? \"-1\" : null)(\"align\", null);\n        i0.ɵɵstyleProp(\"top\", ctx.fixedInViewport ? ctx.fixedTopGap : null, \"px\")(\"bottom\", ctx.fixedInViewport ? ctx.fixedBottomGap : null, \"px\");\n        i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-sidenav-fixed\", ctx.fixedInViewport);\n      }\n    },\n    inputs: {\n      fixedInViewport: \"fixedInViewport\",\n      fixedTopGap: \"fixedTopGap\",\n      fixedBottomGap: \"fixedBottomGap\"\n    },\n    exportAs: [\"matSidenav\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatDrawer,\n      useExisting: MatSidenav\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 0,\n    consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"]],\n    template: function MatSidenav_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [CdkScrollable],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenav, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav',\n      exportAs: 'matSidenav',\n      host: {\n        'class': 'mat-drawer mat-sidenav',\n        // The sidenav container should not be focused on when used in side mode. See b/286459024 for\n        // reference. Updates tabIndex of drawer/container to default to null if in side mode.\n        '[attr.tabIndex]': '(mode !== \"side\") ? \"-1\" : null',\n        // must prevent the browser from aligning text based on value\n        '[attr.align]': 'null',\n        '[class.mat-drawer-end]': 'position === \"end\"',\n        '[class.mat-drawer-over]': 'mode === \"over\"',\n        '[class.mat-drawer-push]': 'mode === \"push\"',\n        '[class.mat-drawer-side]': 'mode === \"side\"',\n        '[class.mat-sidenav-fixed]': 'fixedInViewport',\n        '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n        '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkScrollable],\n      providers: [{\n        provide: MatDrawer,\n        useExisting: MatSidenav\n      }],\n      template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\"\n    }]\n  }], null, {\n    fixedInViewport: [{\n      type: Input\n    }],\n    fixedTopGap: [{\n      type: Input\n    }],\n    fixedBottomGap: [{\n      type: Input\n    }]\n  });\n})();\nclass MatSidenavContainer extends MatDrawerContainer {\n  _allDrawers = undefined;\n  // We need an initializer here to avoid a TS error.\n  _content = undefined;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatSidenavContainer_BaseFactory;\n    return function MatSidenavContainer_Factory(__ngFactoryType__) {\n      return (ɵMatSidenavContainer_BaseFactory || (ɵMatSidenavContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenavContainer)))(__ngFactoryType__ || MatSidenavContainer);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSidenavContainer,\n    selectors: [[\"mat-sidenav-container\"]],\n    contentQueries: function MatSidenavContainer_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatSidenavContent, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatSidenav, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-drawer-container\", \"mat-sidenav-container\"],\n    hostVars: 2,\n    hostBindings: function MatSidenavContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n      }\n    },\n    exportAs: [\"matSidenavContainer\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_DRAWER_CONTAINER,\n      useExisting: MatSidenavContainer\n    }, {\n      provide: MatDrawerContainer,\n      useExisting: MatSidenavContainer\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c5,\n    decls: 4,\n    vars: 2,\n    consts: [[1, \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n    template: function MatSidenavContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c4);\n        i0.ɵɵtemplate(0, MatSidenavContainer_Conditional_0_Template, 1, 2, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵtemplate(3, MatSidenavContainer_Conditional_3_Template, 2, 0, \"mat-sidenav-content\");\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.hasBackdrop ? 0 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(!ctx._content ? 3 : -1);\n      }\n    },\n    dependencies: [MatSidenavContent],\n    styles: [_c6],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav-container',\n      exportAs: 'matSidenavContainer',\n      host: {\n        'class': 'mat-drawer-container mat-sidenav-container',\n        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatSidenavContainer\n      }, {\n        provide: MatDrawerContainer,\n        useExisting: MatSidenavContainer\n      }],\n      imports: [MatSidenavContent],\n      template: \"@if (hasBackdrop) {\\n  <div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\"\\n       [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n}\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n\\n@if (!_content) {\\n  <mat-sidenav-content>\\n    <ng-content></ng-content>\\n  </mat-sidenav-content>\\n}\\n\",\n      styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\"]\n    }]\n  }], null, {\n    _allDrawers: [{\n      type: ContentChildren,\n      args: [MatSidenav, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }],\n    _content: [{\n      type: ContentChild,\n      args: [MatSidenavContent]\n    }]\n  });\n})();\nclass MatSidenavModule {\n  static ɵfac = function MatSidenavModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSidenavModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSidenavModule,\n    imports: [MatCommonModule, CdkScrollableModule, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent],\n    exports: [CdkScrollableModule, MatCommonModule, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, CdkScrollableModule, CdkScrollableModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkScrollableModule, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent],\n      exports: [CdkScrollableModule, MatCommonModule, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material drawers.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matDrawerAnimations = {\n  // Represents\n  // trigger('transform', [\n  //   // We remove the `transform` here completely, rather than setting it to zero, because:\n  //   // 1. Having a transform can cause elements with ripples or an animated\n  //   //    transform to shift around in Chrome with an RTL layout (see #10023).\n  //   // 2. 3d transforms causes text to appear blurry on IE and Edge.\n  //   state(\n  //     'open, open-instant',\n  //     style({\n  //       'transform': 'none',\n  //       'visibility': 'visible',\n  //     }),\n  //   ),\n  //   state(\n  //     'void',\n  //     style({\n  //       // Avoids the shadow showing up when closed in SSR.\n  //       'box-shadow': 'none',\n  //       'visibility': 'hidden',\n  //     }),\n  //   ),\n  //   transition('void => open-instant', animate('0ms')),\n  //   transition(\n  //     'void <=> open, open-instant => void',\n  //     animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)'),\n  //   ),\n  // ])\n  /** Animation that slides a drawer in and out. */\n  transformDrawer: {\n    type: 7,\n    name: 'transform',\n    definitions: [{\n      type: 0,\n      name: 'open, open-instant',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'none',\n          visibility: 'visible'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          'box-shadow': 'none',\n          visibility: 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => open-instant',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '0ms'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: 'void <=> open, open-instant => void',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '400ms cubic-bezier(0.25, 0.8, 0.25, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,GAAG,GAAG;AAC5D,IAAM,MAAM,CAAC,cAAc,sBAAsB,GAAG;AACpD,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,iEAAiE;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,oBAAoB,OAAO,mBAAmB,CAAC;AAAA,EAChE;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,oBAAoB;AACzC,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,GAAG,GAAG;AAC9D,IAAM,MAAM,CAAC,eAAe,uBAAuB,GAAG;AACtD,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,kEAAkE;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,oBAAoB,OAAO,mBAAmB,CAAC;AAAA,EAChE;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,qBAAqB;AAC1C,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,MAAM;AACZ,SAAS,8BAA8B,UAAU;AAC/C,QAAM,MAAM,gDAAgD,QAAQ,IAAI;AAC1E;AAEA,IAAM,8BAA8B,IAAI,eAAe,+BAA+B;AAAA,EACpF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAKD,IAAM,uBAAuB,IAAI,eAAe,sBAAsB;AAMtE,SAAS,sCAAsC;AAC7C,SAAO;AACT;AACA,IAAM,mBAAN,MAAM,0BAAyB,cAAc;AAAA,EAC3C,YAAY,OAAO,QAAQ;AAAA,EAC3B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,aAAa,OAAO,kBAAkB;AAAA,EACtC,cAAc;AACZ,UAAM,aAAa,OAAO,UAAU;AACpC,UAAM,mBAAmB,OAAO,gBAAgB;AAChD,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAM,YAAY,kBAAkB,MAAM;AAAA,EAC5C;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,sBAAsB,UAAU,MAAM;AACpD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,kBAAkB;AAMhB,QAAI,KAAK,UAAU,WAAW;AAC5B,aAAO;AAAA,IACT;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,WAAO,SAAS,QAAQ,MAAM,SAAS,UAAU,MAAM,UAAU,OAAO,QAAQ,IAAI,SAAS,UAAU,IAAI;AAAA,EAC7G;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,IAClC,WAAW,CAAC,GAAG,oBAAoB;AAAA,IACnC,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,eAAe,IAAI,WAAW,gBAAgB,MAAM,IAAI,EAAE,gBAAgB,IAAI,WAAW,gBAAgB,OAAO,IAAI;AACnI,QAAG,YAAY,6BAA6B,IAAI,gBAAgB,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,qCAAqC;AAAA,MACvC;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAIH,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,cAAc,OAAO,UAAU;AAAA,EAC/B,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,gBAAgB,OAAO,YAAY;AAAA,EACnC,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,SAAS;AAAA,EAC5B,wBAAwB,OAAO,oBAAoB;AAAA,EACnD,OAAO,OAAO,UAAU;AAAA,IACtB,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa,OAAO,sBAAsB;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa;AAAA,EACb,uCAAuC;AAAA,EACvC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAElB,YAAQ,UAAU,QAAQ,QAAQ;AAClC,QAAI,UAAU,KAAK,WAAW;AAE5B,UAAI,KAAK,aAAa;AACpB,aAAK,wBAAwB,KAAK;AAAA,MACpC;AACA,WAAK,YAAY;AACjB,WAAK,kBAAkB,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,sBAAsB;AAC3B,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,QAAQ;AAAA;AAAA,EAER,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,OAAO;AACtB,SAAK,gBAAgB,sBAAsB,KAAK;AAAA,EAClD;AAAA,EACA,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhB,IAAI,YAAY;AACd,UAAM,QAAQ,KAAK;AAInB,QAAI,SAAS,MAAM;AACjB,UAAI,KAAK,SAAS,QAAQ;AACxB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,QAAI,UAAU,UAAU,UAAU,WAAW,SAAS,MAAM;AAC1D,cAAQ,sBAAsB,KAAK;AAAA,IACrC;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,OAAO,sBAAsB,KAAK,CAAC;AAAA,EAC1C;AAAA,EACA,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA,EAEA,oBAAoB,IAAI,QAAQ;AAAA;AAAA,EAEhC,gBAAgB,IAAI,QAAQ;AAAA;AAAA,EAE5B;AAAA;AAAA,IAEA,IAAI;AAAA;AAAA,MAA0B;AAAA,IAAI;AAAA;AAAA;AAAA,EAElC,gBAAgB,KAAK,aAAa,KAAK,OAAO,OAAK,CAAC,GAAG,IAAI,MAAM;AAAA,EAAC,CAAC,CAAC;AAAA;AAAA,EAEpE,cAAc,KAAK,kBAAkB,KAAK,OAAO,MAAM,KAAK,MAAM,GAAG,MAAM,MAAS,CAAC;AAAA;AAAA,EAErF,gBAAgB,KAAK,aAAa,KAAK,OAAO,OAAK,CAAC,CAAC,GAAG,IAAI,MAAM;AAAA,EAAC,CAAC,CAAC;AAAA;AAAA,EAErE,cAAc,KAAK,kBAAkB,KAAK,OAAO,MAAM,CAAC,KAAK,MAAM,GAAG,MAAM,MAAS,CAAC;AAAA;AAAA,EAEtF,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA,EAGzB,oBAAoB,IAAI,aAAa;AAAA;AAAA,EAErC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,IAAI,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA,EAC3B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc;AACZ,SAAK,aAAa,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AACrE,UAAI,QAAQ;AACV,YAAI,KAAK,MAAM;AACb,eAAK,uCAAuC,KAAK,KAAK;AAAA,QACxD;AACA,aAAK,WAAW;AAAA,MAClB,WAAW,KAAK,qBAAqB,GAAG;AACtC,aAAK,cAAc,KAAK,cAAc,SAAS;AAAA,MACjD;AAAA,IACF,CAAC;AAMD,SAAK,QAAQ,kBAAkB,MAAM;AACnC,YAAM,UAAU,KAAK,YAAY;AACjC,gBAAU,SAAS,SAAS,EAAE,KAAK,OAAO,WAAS;AACjD,eAAO,MAAM,YAAY,UAAU,CAAC,KAAK,gBAAgB,CAAC,eAAe,KAAK;AAAA,MAChF,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS,KAAK,QAAQ,IAAI,MAAM;AACxE,aAAK,MAAM;AACX,cAAM,gBAAgB;AACtB,cAAM,eAAe;AAAA,MACvB,CAAC,CAAC;AACF,WAAK,iBAAiB,CAAC,KAAK,UAAU,OAAO,SAAS,iBAAiB,KAAK,sBAAsB,GAAG,KAAK,UAAU,OAAO,SAAS,iBAAiB,KAAK,sBAAsB,GAAG,KAAK,UAAU,OAAO,SAAS,oBAAoB,KAAK,sBAAsB,CAAC;AAAA,IACpQ,CAAC;AACD,SAAK,cAAc,UAAU,MAAM;AACjC,WAAK,aAAa,KAAK,KAAK,OAAO;AAAA,IACrC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,SAAS;AAC5B,QAAI,CAAC,KAAK,sBAAsB,YAAY,OAAO,GAAG;AACpD,cAAQ,WAAW;AAEnB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAM,WAAW,MAAM;AACrB,sBAAY;AACZ,2BAAiB;AACjB,kBAAQ,gBAAgB,UAAU;AAAA,QACpC;AACA,cAAM,cAAc,KAAK,UAAU,OAAO,SAAS,QAAQ,QAAQ;AACnE,cAAM,mBAAmB,KAAK,UAAU,OAAO,SAAS,aAAa,QAAQ;AAAA,MAC/E,CAAC;AAAA,IACH;AACA,YAAQ,MAAM,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,UAAU,SAAS;AACrC,QAAI,iBAAiB,KAAK,YAAY,cAAc,cAAc,QAAQ;AAC1E,QAAI,gBAAgB;AAClB,WAAK,YAAY,gBAAgB,OAAO;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,UAAM,UAAU,KAAK,YAAY;AAIjC,YAAQ,KAAK,WAAW;AAAA,MACtB,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,wBAAgB,MAAM;AACpB,gBAAM,gBAAgB,KAAK,WAAW,oBAAoB;AAC1D,cAAI,CAAC,iBAAiB,OAAO,QAAQ,UAAU,YAAY;AACzD,oBAAQ,MAAM;AAAA,UAChB;AAAA,QACF,GAAG;AAAA,UACD,UAAU,KAAK;AAAA,QACjB,CAAC;AACD;AAAA,MACF,KAAK;AACH,aAAK,oBAAoB,0CAA0C;AACnE;AAAA,MACF;AACE,aAAK,oBAAoB,KAAK,SAAS;AACvC;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,aAAa;AACzB,QAAI,KAAK,cAAc,UAAU;AAC/B;AAAA,IACF;AACA,QAAI,KAAK,sCAAsC;AAC7C,WAAK,cAAc,SAAS,KAAK,sCAAsC,WAAW;AAAA,IACpF,OAAO;AACL,WAAK,YAAY,cAAc,KAAK;AAAA,IACtC;AACA,SAAK,uCAAuC;AAAA,EAC9C;AAAA;AAAA,EAEA,uBAAuB;AACrB,UAAM,WAAW,KAAK,KAAK;AAC3B,WAAO,CAAC,CAAC,YAAY,KAAK,YAAY,cAAc,SAAS,QAAQ;AAAA,EACvE;AAAA,EACA,kBAAkB;AAChB,SAAK,cAAc;AAGnB,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,wBAAwB,KAAK;AAAA,IACpC;AAGA,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,aAAa,KAAK,kBAAkB,OAAO,KAAK,YAAY,aAAa;AAC9E,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,QAAQ,aAAW,QAAQ,CAAC;AAChD,SAAK,YAAY,QAAQ;AACzB,SAAK,SAAS,OAAO;AACrB,SAAK,UAAU;AACf,SAAK,kBAAkB,SAAS;AAChC,SAAK,cAAc,SAAS;AAC5B,SAAK,aAAa,SAAS;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,WAAW;AACd,WAAO,KAAK,OAAO,MAAM,SAAS;AAAA,EACpC;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAAA;AAAA,EAEA,yBAAyB;AAIvB,WAAO,KAAK;AAAA;AAAA,MAAqB;AAAA;AAAA,MAAyB;AAAA,MAAM;AAAA,IAAO;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAAS,CAAC,KAAK,QAAQ,WAAW;AAGvC,QAAI,UAAU,WAAW;AACvB,WAAK,aAAa;AAAA,IACpB;AACA,UAAM,SAAS,KAAK;AAAA,MAAS;AAAA;AAAA,MAA0B,CAAC,UAAU,KAAK,qBAAqB;AAAA,MAAG,KAAK,cAAc;AAAA,IAAS;AAC3H,QAAI,CAAC,QAAQ;AACX,WAAK,aAAa;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,QAAQ,cAAc,aAAa;AAC1C,QAAI,WAAW,KAAK,SAAS;AAC3B,aAAO,QAAQ,QAAQ,SAAS,SAAS,OAAO;AAAA,IAClD;AACA,SAAK,UAAU;AACf,QAAI,KAAK,YAAY,qBAAqB;AAGxC,WAAK,gBAAgB,IAAI;AAAA,IAC3B,OAAO;AAEL,iBAAW,MAAM;AACf,aAAK,kBAAkB,KAAK;AAC5B,aAAK,cAAc,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,YAAY,cAAc,UAAU,OAAO,qBAAqB,MAAM;AAC3E,QAAI,CAAC,UAAU,cAAc;AAC3B,WAAK,cAAc,WAAW;AAAA,IAChC;AAEA,SAAK,mBAAmB,aAAa;AACrC,SAAK,sBAAsB;AAC3B,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,aAAa,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,UAAQ,QAAQ,OAAO,SAAS,OAAO,CAAC;AAAA,IACpF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB,aAAa;AAC3B,SAAK,YAAY,cAAc,UAAU,OAAO,wBAAwB,WAAW;AAAA,EACrF;AAAA,EACA,YAAY;AACV,WAAO,KAAK,YAAY,cAAc,eAAe;AAAA,EACvD;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,KAAK,YAAY;AAGnB,WAAK,WAAW,UAAU,CAAC,CAAC,KAAK,YAAY,eAAe,KAAK;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,aAAa;AAEnC,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B;AAAA,IACF;AACA,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,SAAS,QAAQ;AACvB,QAAI,gBAAgB,OAAO;AACzB,UAAI,CAAC,KAAK,SAAS;AACjB,aAAK,UAAU,KAAK,KAAK,cAAc,mBAAmB;AAC1D,eAAO,aAAa,KAAK,SAAS,OAAO;AAAA,MAC3C;AACA,aAAO,YAAY,OAAO;AAAA,IAC5B,WAAW,KAAK,SAAS;AACvB,WAAK,QAAQ,WAAW,aAAa,SAAS,KAAK,OAAO;AAAA,IAC5D;AAAA,EACF;AAAA;AAAA,EAEA,yBAAyB,WAAS;AAChC,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,MAAM,WAAW,SAAS;AAC5B,WAAK,QAAQ,IAAI,MAAM;AACrB,YAAI,MAAM,SAAS,iBAAiB;AAClC,eAAK,kBAAkB,KAAK,KAAK;AAAA,QACnC,OAAO;AAGL,cAAI,MAAM,SAAS,iBAAiB;AAClC,iBAAK,gBAAgB,KAAK;AAAA,UAC5B;AACA,eAAK,cAAc,KAAK,KAAK;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,MACjE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,YAAY;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,SAAS,IAAI,EAAE,YAAY,IAAI,SAAS,SAAS,OAAO,IAAI;AAC3E,QAAG,YAAY,cAAc,CAAC,IAAI,cAAc,CAAC,IAAI,SAAS,WAAW,IAAI;AAC7E,QAAG,YAAY,kBAAkB,IAAI,aAAa,KAAK,EAAE,mBAAmB,IAAI,SAAS,MAAM,EAAE,mBAAmB,IAAI,SAAS,MAAM,EAAE,mBAAmB,IAAI,SAAS,MAAM;AAAA,MACjL;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,MACN,cAAc;AAAA,MACd,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,MACd,eAAe;AAAA,MACf,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,MACb,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,IAAI,GAAG,4BAA4B,CAAC;AAAA,IAChF,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA,QAET,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA;AAAA;AAAA;AAAA,QAI3B,sBAAsB;AAAA;AAAA;AAAA,QAGtB,mBAAmB;AAAA,MACrB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,aAAa;AAAA,MACvB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,OAAO,UAAU;AAAA,EAC5B,UAAU,OAAO,MAAM;AAAA,EACvB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,sBAAsB;AAAA;AAAA,EAEtB;AAAA;AAAA,EAEA,WAAW,IAAI,UAAU;AAAA,EACzB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA,EACA,YAAY,OAAO,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9C,IAAI,cAAc;AAChB,WAAO,KAAK,mBAAmB,KAAK,MAAM,KAAK,KAAK,mBAAmB,KAAK,IAAI;AAAA,EAClF;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,oBAAoB,SAAS,OAAO,OAAO,sBAAsB,KAAK;AAAA,EAC7E;AAAA,EACA;AAAA;AAAA,EAEA,gBAAgB,IAAI,aAAa;AAAA;AAAA,EAEjC;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA,EACA;AAAA;AAAA,EAEA,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB,kBAAkB,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,kBAAkB;AAAA,IAChB,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,wBAAwB,IAAI,QAAQ;AAAA;AAAA,EAEpC,IAAI,aAAa;AACf,WAAO,KAAK,gBAAgB,KAAK;AAAA,EACnC;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,WAAW,OAAO,QAAQ;AAChC,UAAM,gBAAgB,OAAO,aAAa;AAG1C,SAAK,MAAM,OAAO,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AACjE,WAAK,iBAAiB;AACtB,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAGD,kBAAc,OAAO,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,qBAAqB,CAAC;AACnG,QAAI,KAAK,mBAAmB,oBAAoB,SAAS,WAAW;AAClE,WAAK,QAAQ,kBAAkB,MAAM;AAGnC,mBAAW,MAAM;AACf,eAAK,SAAS,cAAc,UAAU,IAAI,uBAAuB;AACjE,eAAK,sBAAsB;AAAA,QAC7B,GAAG,GAAG;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,YAAY,QAAQ,KAAK,UAAU,KAAK,WAAW,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AACzG,WAAK,SAAS,MAAM,OAAO,OAAO,UAAQ,CAAC,KAAK,cAAc,KAAK,eAAe,IAAI,CAAC;AACvF,WAAK,SAAS,gBAAgB;AAAA,IAChC,CAAC;AACD,SAAK,SAAS,QAAQ,KAAK,UAAU,IAAI,CAAC,EAAE,UAAU,MAAM;AAC1D,WAAK,iBAAiB;AACtB,WAAK,SAAS,QAAQ,YAAU;AAC9B,aAAK,mBAAmB,MAAM;AAC9B,aAAK,qBAAqB,MAAM;AAChC,aAAK,iBAAiB,MAAM;AAAA,MAC9B,CAAC;AACD,UAAI,CAAC,KAAK,SAAS,UAAU,KAAK,cAAc,KAAK,MAAM,KAAK,KAAK,cAAc,KAAK,IAAI,GAAG;AAC7F,aAAK,qBAAqB;AAAA,MAC5B;AACA,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAED,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,gBAAgB;AAAA,QAAK,aAAa,EAAE;AAAA;AAAA,QAEzC,UAAU,KAAK,UAAU;AAAA,MAAC,EAAE,UAAU,MAAM,KAAK,qBAAqB,CAAC;AAAA,IACzE,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB,SAAS;AACpC,SAAK,gBAAgB,SAAS;AAC9B,SAAK,SAAS,QAAQ;AACtB,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,OAAO;AACL,SAAK,SAAS,QAAQ,YAAU,OAAO,KAAK,CAAC;AAAA,EAC/C;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,SAAS,QAAQ,YAAU,OAAO,MAAM,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AAOrB,QAAI,OAAO;AACX,QAAI,QAAQ;AACZ,QAAI,KAAK,SAAS,KAAK,MAAM,QAAQ;AACnC,UAAI,KAAK,MAAM,QAAQ,QAAQ;AAC7B,gBAAQ,KAAK,MAAM,UAAU;AAAA,MAC/B,WAAW,KAAK,MAAM,QAAQ,QAAQ;AACpC,cAAM,QAAQ,KAAK,MAAM,UAAU;AACnC,gBAAQ;AACR,iBAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,KAAK,UAAU,KAAK,OAAO,QAAQ;AACrC,UAAI,KAAK,OAAO,QAAQ,QAAQ;AAC9B,iBAAS,KAAK,OAAO,UAAU;AAAA,MACjC,WAAW,KAAK,OAAO,QAAQ,QAAQ;AACrC,cAAM,QAAQ,KAAK,OAAO,UAAU;AACpC,iBAAS;AACT,gBAAQ;AAAA,MACV;AAAA,IACF;AAKA,WAAO,QAAQ;AACf,YAAQ,SAAS;AACjB,QAAI,SAAS,KAAK,gBAAgB,QAAQ,UAAU,KAAK,gBAAgB,OAAO;AAC9E,WAAK,kBAAkB;AAAA,QACrB;AAAA,QACA;AAAA,MACF;AAGA,WAAK,QAAQ,IAAI,MAAM,KAAK,sBAAsB,KAAK,KAAK,eAAe,CAAC;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,YAAY;AAEV,QAAI,KAAK,aAAa,KAAK,UAAU,GAAG;AAEtC,WAAK,QAAQ,kBAAkB,MAAM,KAAK,gBAAgB,KAAK,CAAC;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,QAAQ;AACzB,WAAO,kBAAkB,KAAK,UAAU,KAAK,SAAS,OAAO,CAAC,EAAE,UAAU,MAAM;AAC9E,WAAK,qBAAqB;AAC1B,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,OAAO,SAAS,QAAQ;AAC1B,aAAO,aAAa,KAAK,UAAU,KAAK,SAAS,OAAO,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,OAAO,MAAM,CAAC;AAAA,IACnH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,QAAQ;AAG3B,WAAO,kBAAkB,KAAK,UAAU,KAAK,SAAS,OAAO,CAAC,EAAE,UAAU,MAAM;AAC9E,sBAAgB;AAAA,QACd,MAAM,MAAM,KAAK,iBAAiB;AAAA,MACpC,GAAG;AAAA,QACD,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,iBAAiB,QAAQ;AACvB,WAAO,aAAa,KAAK,UAAU,MAAM,KAAK,SAAS,SAAS,KAAK,UAAU,CAAC,CAAC,EAAE,UAAU,MAAM;AACjG,WAAK,qBAAqB;AAC1B,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,UAAM,YAAY,KAAK,SAAS,cAAc;AAC9C,UAAM,YAAY;AAClB,QAAI,OAAO;AACT,gBAAU,IAAI,SAAS;AAAA,IACzB,OAAO;AACL,gBAAU,OAAO,SAAS;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,SAAS,KAAK,OAAO;AAE1B,SAAK,SAAS,QAAQ,YAAU;AAC9B,UAAI,OAAO,YAAY,OAAO;AAC5B,YAAI,KAAK,QAAQ,SAAS,OAAO,cAAc,eAAe,YAAY;AACxE,wCAA8B,KAAK;AAAA,QACrC;AACA,aAAK,OAAO;AAAA,MACd,OAAO;AACL,YAAI,KAAK,UAAU,SAAS,OAAO,cAAc,eAAe,YAAY;AAC1E,wCAA8B,OAAO;AAAA,QACvC;AACA,aAAK,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AACD,SAAK,SAAS,KAAK,QAAQ;AAE3B,QAAI,KAAK,QAAQ,KAAK,KAAK,UAAU,OAAO;AAC1C,WAAK,QAAQ,KAAK;AAClB,WAAK,SAAS,KAAK;AAAA,IACrB,OAAO;AACL,WAAK,QAAQ,KAAK;AAClB,WAAK,SAAS,KAAK;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,cAAc,KAAK,MAAM,KAAK,KAAK,OAAO,QAAQ,UAAU,KAAK,cAAc,KAAK,IAAI,KAAK,KAAK,KAAK,QAAQ;AAAA,EAC7H;AAAA,EACA,qBAAqB;AACnB,SAAK,cAAc,KAAK;AACxB,SAAK,8BAA8B;AAAA,EACrC;AAAA,EACA,gCAAgC;AAE9B,KAAC,KAAK,QAAQ,KAAK,IAAI,EAAE,OAAO,YAAU,UAAU,CAAC,OAAO,gBAAgB,KAAK,mBAAmB,MAAM,CAAC,EAAE,QAAQ,YAAU,OAAO,uBAAuB,CAAC;AAAA,EAChK;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,cAAc,KAAK,MAAM,KAAK,KAAK,mBAAmB,KAAK,MAAM,KAAK,KAAK,cAAc,KAAK,IAAI,KAAK,KAAK,mBAAmB,KAAK,IAAI;AAAA,EACtJ;AAAA,EACA,cAAc,QAAQ;AACpB,WAAO,UAAU,QAAQ,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,mBAAmB,QAAQ;AACzB,QAAI,KAAK,qBAAqB,MAAM;AAClC,aAAO,CAAC,CAAC,UAAU,OAAO,SAAS;AAAA,IACrC;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,gBAAgB,SAAS,kCAAkC,IAAI,KAAK,UAAU;AAC5E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAC/C,QAAG,eAAe,UAAU,WAAW,CAAC;AAAA,MAC1C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc;AAAA,MACjE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kBAAkB,CAAC;AAAA,MACpC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,IACrC,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0CAA0C,IAAI,iBAAiB;AAAA,MAChF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,uBAAuB,GAAG,kBAAkB,GAAG,CAAC,GAAG,uBAAuB,GAAG,OAAO,CAAC;AAAA,IAClG,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,OAAO,CAAC;AAC1E,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,oBAAoB;AAAA,MACxF;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,cAAc,IAAI,EAAE;AACzC,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,WAAW,IAAI,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAgB;AAAA,IAC/B,QAAQ,CAAC,igJAAigJ;AAAA,IAC1gJ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,kDAAkD;AAAA,MACpD;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,QAAQ,CAAC,igJAAigJ;AAAA,IAC5gJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA;AAAA;AAAA,QAGhB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,2BAA0B,iBAAiB;AAAA,EAC/C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,0BAA0B,mBAAmB;AAC3D,cAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,qBAAqB,kBAAiB;AAAA,IAClK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,WAAW,CAAC,GAAG,sBAAsB,qBAAqB;AAAA,IAC1D,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,aAAN,MAAM,oBAAmB,UAAU;AAAA;AAAA,EAEjC,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,SAAK,mBAAmB,sBAAsB,KAAK;AAAA,EACrD;AAAA,EACA,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,qBAAqB,KAAK;AAAA,EAChD;AAAA,EACA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,kBAAkB,qBAAqB,KAAK;AAAA,EACnD;AAAA,EACA,kBAAkB;AAAA,EAClB,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,WAAW,CAAC,GAAG,cAAc,aAAa;AAAA,IAC1C,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,IAAI,SAAS,SAAS,OAAO,IAAI,EAAE,SAAS,IAAI;AAC3E,QAAG,YAAY,OAAO,IAAI,kBAAkB,IAAI,cAAc,MAAM,IAAI,EAAE,UAAU,IAAI,kBAAkB,IAAI,iBAAiB,MAAM,IAAI;AACzI,QAAG,YAAY,kBAAkB,IAAI,aAAa,KAAK,EAAE,mBAAmB,IAAI,SAAS,MAAM,EAAE,mBAAmB,IAAI,SAAS,MAAM,EAAE,mBAAmB,IAAI,SAAS,MAAM,EAAE,qBAAqB,IAAI,eAAe;AAAA,MAC3N;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,IACvB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,IAAI,GAAG,4BAA4B,CAAC;AAAA,IAChF,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA,QAGT,mBAAmB;AAAA;AAAA,QAEnB,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,6BAA6B;AAAA,QAC7B,kBAAkB;AAAA,QAClB,qBAAqB;AAAA,MACvB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,aAAa;AAAA,MACvB,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,6BAA4B,mBAAmB;AAAA,EACnD,cAAc;AAAA;AAAA,EAEd,WAAW;AAAA,EACX,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,4BAA4B,mBAAmB;AAC7D,cAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,IAC1K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,gBAAgB,SAAS,mCAAmC,IAAI,KAAK,UAAU;AAC7E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,mBAAmB,CAAC;AAChD,QAAG,eAAe,UAAU,YAAY,CAAC;AAAA,MAC3C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc;AAAA,MACjE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,wBAAwB,uBAAuB;AAAA,IAC9D,UAAU;AAAA,IACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0CAA0C,IAAI,iBAAiB;AAAA,MAChF;AAAA,IACF;AAAA,IACA,UAAU,CAAC,qBAAqB;AAAA,IAChC,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,uBAAuB,GAAG,kBAAkB,GAAG,CAAC,GAAG,uBAAuB,GAAG,OAAO,CAAC;AAAA,IAClG,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,OAAO,CAAC;AAC3E,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,qBAAqB;AAAA,MAC1F;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,cAAc,IAAI,EAAE;AACzC,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,WAAW,IAAI,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAiB;AAAA,IAChC,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,kDAAkD;AAAA,MACpD;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,iBAAiB;AAAA,MAC3B,UAAU;AAAA,MACV,QAAQ,CAAC,igJAAigJ;AAAA,IAC5gJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA;AAAA;AAAA,QAGjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,qBAAqB,WAAW,oBAAoB,kBAAkB,YAAY,qBAAqB,iBAAiB;AAAA,IACnJ,SAAS,CAAC,qBAAqB,iBAAiB,WAAW,oBAAoB,kBAAkB,YAAY,qBAAqB,iBAAiB;AAAA,EACrJ,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,qBAAqB,qBAAqB,eAAe;AAAA,EACtF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,qBAAqB,WAAW,oBAAoB,kBAAkB,YAAY,qBAAqB,iBAAiB;AAAA,MACnJ,SAAS,CAAC,qBAAqB,iBAAiB,WAAW,oBAAoB,kBAAkB,YAAY,qBAAqB,iBAAiB;AAAA,IACrJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6B1B,iBAAiB;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,cAAc;AAAA,UACd,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}