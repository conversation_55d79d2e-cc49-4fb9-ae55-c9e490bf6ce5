spring.application.name=notification-service
spring.config.import=optional:configserver:http://localhost:8888
spring.cloud.config.fail-fast=true
server.port=8085

spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=notification-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

# Topics Kafka (fallback configuration)
kafka.topics.invitation-responded=invitation.responded

# Mail configuration (fallback configuration)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=rkzn kjzi wbwz tgum
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
