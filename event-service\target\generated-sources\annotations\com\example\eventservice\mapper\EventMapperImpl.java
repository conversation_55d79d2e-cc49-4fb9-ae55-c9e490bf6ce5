package com.example.eventservice.mapper;

import com.example.eventservice.entity.EventEntity;
import com.example.eventservice.model.EventRequest;
import com.example.eventservice.model.EventResponse;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T21:29:25+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250624-0847, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class EventMapperImpl implements EventMapper {

    @Override
    public EventEntity toEntity(EventRequest request) {
        if ( request == null ) {
            return null;
        }

        EventEntity.EventEntityBuilder eventEntity = EventEntity.builder();

        eventEntity.description( request.getDescription() );
        eventEntity.eventDate( request.getEventDate() );
        eventEntity.location( request.getLocation() );
        eventEntity.title( request.getTitle() );

        return eventEntity.build();
    }

    @Override
    public EventResponse toResponse(EventEntity entity) {
        if ( entity == null ) {
            return null;
        }

        EventResponse eventResponse = new EventResponse();

        eventResponse.setCreatedAt( entity.getCreatedAt() );
        eventResponse.setDescription( entity.getDescription() );
        eventResponse.setEventDate( entity.getEventDate() );
        eventResponse.setId( entity.getId() );
        eventResponse.setLocation( entity.getLocation() );
        eventResponse.setOrganizerId( entity.getOrganizerId() );
        eventResponse.setTitle( entity.getTitle() );
        eventResponse.setUpdatedAt( entity.getUpdatedAt() );

        return eventResponse;
    }

    @Override
    public List<EventResponse> toResponseList(List<EventEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<EventResponse> list = new ArrayList<EventResponse>( entities.size() );
        for ( EventEntity eventEntity : entities ) {
            list.add( toResponse( eventEntity ) );
        }

        return list;
    }
}
