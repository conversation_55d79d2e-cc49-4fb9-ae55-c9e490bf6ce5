:host {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.main-content {
  padding: 20px;
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Layout de base */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background);
}

/* Barre d'outils principale */
.main-toolbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 70px;
  padding: 0;

  &.scrolled {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    height: 60px;
  }

  .toolbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    height: 100%;
  }
}

/* Logo de l'application */
.app-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  text-decoration: none;
  transition: transform 0.2s ease;

  span {
    font-size: 1.4rem;
    font-weight: 600;
    background: linear-gradient(45deg, #2196F3, #1976D2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 0.5px;
  }

  &:hover {
    transform: translateY(-1px);
  }
}

/* Liens de navigation */
.nav-links {
  display: flex;
  gap: 1rem;
  height: 100%;
  margin-left: 3rem;

  a {
    position: relative;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 0 1rem;
    height: 100%;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 3px;
      background: linear-gradient(45deg, #2196F3, #1976D2);
      transition: width 0.3s ease;
      border-radius: 3px;
    }

    &:hover {
      color: #2196F3;

      &::after {
        width: calc(100% - 2rem);
      }
    }

    &.active {
      color: #2196F3;

      &::after {
        width: calc(100% - 2rem);
      }
    }
  }
}

/* Section utilisateur */
.user-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;

  .loading-spinner {
    display: flex;
    align-items: center;
  }

  .auth-buttons {
    display: flex;
    gap: 1rem;

    button {
      padding: 0.5rem 1.25rem;
      border-radius: 25px;
      font-weight: 500;
      transition: all 0.3s ease;

      &.mat-raised-button {
        background: linear-gradient(45deg, #2196F3, #1976D2);
        color: white;
        border: none;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }
      }

      &.mat-stroked-button {
        border: 2px solid #2196F3;
        color: #2196F3;

        &:hover {
          background: rgba(33, 150, 243, 0.1);
        }
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 1rem;

    .welcome-text {
      font-weight: 500;
      color: #333;
    }

    .logout-button {
      color: #666;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.05);
        color: #f44336;
      }
    }
  }
}

/* Contenu principal */
.main-content {
  flex: 1;
  margin-top: 64px;
  padding: 24px;
  max-width: 1200px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  
  @media (max-width: 600px) {
    padding: 16px;
  }
}

/* Bannière de bienvenue */
.welcome-banner {
  background: linear-gradient(135deg, var(--primary-color), #5c6bc0);
  color: white;
  padding: 32px 24px;
  border-radius: 8px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 2rem;
    font-weight: 500;
    line-height: 1.2;
  }
  
  p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
  }
  
  @media (max-width: 600px) {
    padding: 24px 16px;
    
    h1 {
      font-size: 1.5rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
}

/* Pied de page */
.app-footer {
  background-color: var(--dark-text);
  color: white;
  padding: 16px 0;
  margin-top: 32px;
  
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
    font-size: 0.875rem;
    opacity: 0.8;
  }
}

/* Styles responsifs */
@media (max-width: 768px) {
  .toolbar-content {
    padding: 0 1rem;
  }

  .nav-links {
    margin-left: 1rem;
    gap: 0.5rem;

    a {
      padding: 0 0.75rem;
    }
  }

  .app-logo span {
    font-size: 1.2rem;
  }

  .user-section {
    gap: 0.75rem;

    .welcome-text {
      display: none;
    }
  }
}

/* Animation de chargement */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Conteneur des boutons d'authentification */
.auth-buttons-container {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  
  .loading-spinner {
    margin: 0 16px;
  }
}

/* Styles pour les boutons d'authentification */
.auth-button {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  padding: 0 12px;
  height: 100%;
  color: rgb(215, 12, 12);
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .button-text {
    @media (max-width: 600px) {
      display: none;
    }
  }
  
  mat-icon {
    margin: 0;
    width: 24px;
    height: 24px;
    font-size: 24px;
  }
}

/* Indicateur de chargement */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  
  mat-spinner {
    width: 24px !important;
    height: 24px !important;
    
    ::ng-deep circle {
      stroke: white;
    }
  }
}

/* Amélioration du menu utilisateur */
.user-menu {
  .user-button {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 100%;
    color: white;
    text-transform: none;
    
    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      
      mat-icon {
        color: rgb(6, 6, 6);
      }
      
      .user-initials {
        color: white;
        font-weight: 500;
      }
      
      &.large {
        width: 48px;
        height: 48px;
        font-size: 24px;
      }
    }
    
    .user-info {
      display: flex;
      flex-direction: column;
      text-align: left;
      line-height: 1.2;
      
      .user-name {
        font-weight: 500;
      }
      
      .user-email {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
}

/* Styles pour le menu déroulant utilisateur */
.user-dropdown {
  .user-dropdown-header {
    display: flex;
    align-items: center;
    padding: 16px;
    gap: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 8px;
  }
  
  .mat-menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    
    mat-icon {
      margin-right: 0;
    }
  }
}

/* Bouton de déconnexion dans la barre latérale */
.sidenav-footer {
  padding: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  
  .logout-button {
    width: 100%;
    justify-content: flex-start;
    padding: 0 16px;
    
    mat-icon {
      margin-right: 8px;
    }
  }
}