spring.application.name=api-gateway
server.port=8093

# Configuration Gateway
spring.cloud.gateway.discovery.locator.enabled=true

# Configuration Eureka
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.instance.hostname=localhost
eureka.client.fetch-registry=true
eureka.client.register-with-eureka=true
eureka.instance.prefer-ip-address=true

# Configuration Keycloak
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/RepasKeycloak/protocol/openid-connect/certs

# Optional client configuration
spring.security.oauth2.client.provider.keycloak.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.client.provider.keycloak.user-name-attribute=preferred_username
spring.security.oauth2.client.registration.keycloak.client-id=repas-service
spring.security.oauth2.client.registration.keycloak.client-secret=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq
spring.security.oauth2.client.registration.keycloak.scope=openid,profile,email,roles

# Mode reactif pour Spring Cloud Gateway
spring.main.web-application-type=reactive

# Configuration JWT - Utiliser la cl? secr?te Keycloak
jwt.secret-key=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq

# Routes Gateway avec ordre de priorité (plus petit = plus prioritaire)

# Route pour les événements (Event Service) - Haute priorité
spring.cloud.gateway.routes[0].id=event-service
spring.cloud.gateway.routes[0].uri=lb://EVENT
spring.cloud.gateway.routes[0].predicates[0]=Path=/events/**
spring.cloud.gateway.routes[0].order=1

# Route pour les invitations (Invitation Service)
spring.cloud.gateway.routes[1].id=invitation-service
spring.cloud.gateway.routes[1].uri=lb://INVITATION-SERVICE
spring.cloud.gateway.routes[1].predicates[0]=Path=/invitations/**
spring.cloud.gateway.routes[1].order=2

# Route pour l'authentification (User Service)
spring.cloud.gateway.routes[2].id=user-service-auth
spring.cloud.gateway.routes[2].uri=lb://USER
spring.cloud.gateway.routes[2].predicates[0]=Path=/auth/**
spring.cloud.gateway.routes[2].order=3

# Route pour l'API utilisateurs (User Service)
spring.cloud.gateway.routes[3].id=user-service-api
spring.cloud.gateway.routes[3].uri=lb://USER
spring.cloud.gateway.routes[3].predicates[0]=Path=/api/users/**
spring.cloud.gateway.routes[3].order=4

# Route pour les mots de passe (User Service)
spring.cloud.gateway.routes[4].id=user-service-password
spring.cloud.gateway.routes[4].uri=lb://USER
spring.cloud.gateway.routes[4].predicates[0]=Path=/api/password/**
spring.cloud.gateway.routes[4].order=5

# Route pour les notifications (Notification Service)
spring.cloud.gateway.routes[5].id=notification-service
spring.cloud.gateway.routes[5].uri=lb://NOTIFICATION
spring.cloud.gateway.routes[5].predicates[0]=Path=/notifications/**
spring.cloud.gateway.routes[5].order=6

# Route pour Actuator/Health checks
spring.cloud.gateway.routes[6].id=actuator
spring.cloud.gateway.routes[6].uri=lb://USER
spring.cloud.gateway.routes[6].predicates[0]=Path=/actuator/**
spring.cloud.gateway.routes[6].order=7

# Configuration CORS
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-origins=http://localhost:3000,http://localhost:4200,http://localhost:8093
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-headers=*
spring.cloud.gateway.globalcors.cors-configurations.[/**].allow-credentials=true

# D?sactiver le v?rificateur de compatibilit? Spring Cloud
spring.cloud.compatibility-verifier.enabled=false

# Activer les endpoints pour rafra?chir la config
management.endpoints.web.exposure.include=refresh
management.tracing.sampling.probability=1.0

# Logging Configuration
logging.level.org.springframework.cloud.gateway=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.reactor.netty=DEBUG
logging.level.redisratelimiter=DEBUG
logging.level.org.springframework.web.cors=DEBUG

# Message de bienvenue
welcome.message=Bienvenue dans l'API Gateway!
